import 'package:freezed_annotation/freezed_annotation.dart';

part 'failures.freezed.dart';

/// Base class for all failures in the application
@freezed
class Failure with _$Failure {
  const factory Failure.server({
    required String message,
    String? code,
    @Default(500) int statusCode,
  }) = ServerFailure;

  const factory Failure.network({
    required String message,
    String? code,
  }) = NetworkFailure;

  const factory Failure.cache({
    required String message,
    String? code,
  }) = CacheFailure;

  const factory Failure.auth({
    required String message,
    String? code,
  }) = AuthFailure;

  const factory Failure.validation({
    required String message,
    String? field,
  }) = ValidationFailure;

  const factory Failure.permission({
    required String message,
    String? code,
  }) = PermissionFailure;

  const factory Failure.notFound({
    required String message,
    String? resource,
  }) = NotFoundFailure;

  const factory Failure.unknown({
    required String message,
    Object? exception,
  }) = UnknownFailure;
}

/// Extension to provide user-friendly error messages
extension FailureExtension on Failure {
  String get userMessage {
    return when(
      server: (message, code, statusCode) => 
        'Server error occurred. Please try again later.',
      network: (message, code) => 
        'Network connection failed. Please check your internet connection.',
      cache: (message, code) => 
        'Local storage error occurred.',
      auth: (message, code) => 
        'Authentication failed. Please log in again.',
      validation: (message, field) => 
        field != null ? 'Invalid $field: $message' : message,
      permission: (message, code) => 
        'Permission denied. $message',
      notFound: (message, resource) => 
        resource != null ? '$resource not found' : message,
      unknown: (message, exception) => 
        'An unexpected error occurred. Please try again.',
    );
  }

  String get debugMessage {
    return when(
      server: (message, code, statusCode) => 
        'ServerFailure: $message (Code: $code, Status: $statusCode)',
      network: (message, code) => 
        'NetworkFailure: $message (Code: $code)',
      cache: (message, code) => 
        'CacheFailure: $message (Code: $code)',
      auth: (message, code) => 
        'AuthFailure: $message (Code: $code)',
      validation: (message, field) => 
        'ValidationFailure: $message (Field: $field)',
      permission: (message, code) => 
        'PermissionFailure: $message (Code: $code)',
      notFound: (message, resource) => 
        'NotFoundFailure: $message (Resource: $resource)',
      unknown: (message, exception) => 
        'UnknownFailure: $message (Exception: $exception)',
    );
  }
}
