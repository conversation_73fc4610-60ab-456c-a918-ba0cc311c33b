import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

/// Firebase Auth instance provider
final firebaseAuthProvider = Provider<FirebaseAuth>((ref) {
  return FirebaseAuth.instance;
});

/// Firestore instance provider
final firestoreProvider = Provider<FirebaseFirestore>((ref) {
  return FirebaseFirestore.instance;
});

/// Firebase Storage instance provider
final firebaseStorageProvider = Provider<FirebaseStorage>((ref) {
  return FirebaseStorage.instance;
});

/// HTTP client provider
final httpClientProvider = Provider<http.Client>((ref) {
  return http.Client();
});

/// Secure storage provider
final secureStorageProvider = Provider<FlutterSecureStorage>((ref) {
  return const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );
});

/// Shared preferences provider
final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('SharedPreferences must be overridden with actual instance');
});

/// Current user provider
final currentUserProvider = StreamProvider<User?>((ref) {
  final auth = ref.watch(firebaseAuthProvider);
  return auth.authStateChanges();
});

/// Current user ID provider
final currentUserIdProvider = Provider<String?>((ref) {
  final user = ref.watch(currentUserProvider).value;
  return user?.uid;
});

/// App configuration provider
final appConfigProvider = Provider<AppConfig>((ref) {
  return const AppConfig();
});

/// Application configuration class
class AppConfig {
  const AppConfig();
  
  // API Configuration
  static const String baseUrl = 'https://api.moneymouthy.com';
  static const Duration requestTimeout = Duration(seconds: 30);
  
  // Stripe Configuration (use environment variables in production)
  static const String stripePublishableKey = 'pk_test_...';
  static const String stripeSecretKey = 'sk_test_...';
  
  // Cache Configuration
  static const Duration cacheExpiration = Duration(hours: 1);
  static const int maxCacheSize = 100;
  
  // Pagination Configuration
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // File Upload Configuration
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  static const List<String> allowedVideoTypes = ['mp4', 'mov', 'avi'];
  
  // Validation Configuration
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 30;
  static const int minPasswordLength = 8;
  static const int maxPostContentLength = 5000;
  static const double minPostPrice = 0.01;
  static const double maxPostPrice = 1000.0;
}

/// Logger provider for debugging and monitoring
final loggerProvider = Provider<AppLogger>((ref) {
  return AppLogger();
});

/// Simple logger implementation
class AppLogger {
  void debug(String message, [Object? error, StackTrace? stackTrace]) {
    print('[DEBUG] $message');
    if (error != null) print('[DEBUG] Error: $error');
    if (stackTrace != null) print('[DEBUG] StackTrace: $stackTrace');
  }
  
  void info(String message) {
    print('[INFO] $message');
  }
  
  void warning(String message, [Object? error]) {
    print('[WARNING] $message');
    if (error != null) print('[WARNING] Error: $error');
  }
  
  void error(String message, [Object? error, StackTrace? stackTrace]) {
    print('[ERROR] $message');
    if (error != null) print('[ERROR] Error: $error');
    if (stackTrace != null) print('[ERROR] StackTrace: $stackTrace');
  }
}
