import 'package:freezed_annotation/freezed_annotation.dart';

part 'entities.freezed.dart';

/// Base entity class with common properties
abstract class Entity {
  const Entity();

  String get id;
  DateTime get createdAt;
  DateTime? get updatedAt;
}

/// Value object for unique identifiers
@freezed
class UniqueId with _$UniqueId {
  const factory UniqueId(String value) = _UniqueId;

  factory UniqueId.generate() {
    return UniqueId(DateTime.now().millisecondsSinceEpoch.toString());
  }
}

/// Value object for email addresses
@freezed
class EmailAddress with _$EmailAddress {
  const factory EmailAddress._(String value) = _EmailAddress;

  factory EmailAddress(String input) {
    if (_isValidEmail(input)) {
      return EmailAddress._(input.toLowerCase().trim());
    }
    throw ArgumentError('Invalid email address: $input');
  }

  static bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
}

/// Value object for usernames
@freezed
class Username with _$Username {
  const factory Username._(String value) = _Username;

  factory Username(String input) {
    final trimmed = input.trim();
    if (_isValidUsername(trimmed)) {
      return Username._(trimmed);
    }
    throw ArgumentError('Invalid username: $input');
  }

  static bool _isValidUsername(String username) {
    return username.length >= 3 &&
        username.length <= 30 &&
        RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(username);
  }
}

/// Value object for monetary amounts
@freezed
class Money with _$Money {
  const Money._();

  const factory Money({
    required double amount,
    @Default('USD') String currency,
  }) = _Money;

  factory Money.zero() => const Money(amount: 0.0);

  factory Money.fromCents(int cents, {String currency = 'USD'}) {
    return Money(amount: cents / 100.0, currency: currency);
  }

  int get cents => (amount * 100).round();

  Money operator +(Money other) {
    if (currency != other.currency) {
      throw ArgumentError('Cannot add different currencies');
    }
    return Money(amount: amount + other.amount, currency: currency);
  }

  Money operator -(Money other) {
    if (currency != other.currency) {
      throw ArgumentError('Cannot subtract different currencies');
    }
    return Money(amount: amount - other.amount, currency: currency);
  }

  bool operator >(Money other) {
    if (currency != other.currency) {
      throw ArgumentError('Cannot compare different currencies');
    }
    return amount > other.amount;
  }

  bool operator <(Money other) {
    if (currency != other.currency) {
      throw ArgumentError('Cannot compare different currencies');
    }
    return amount < other.amount;
  }

  bool operator >=(Money other) => this > other || this == other;
  bool operator <=(Money other) => this < other || this == other;

  bool get isPositive => amount > 0;
  bool get isNegative => amount < 0;
  bool get isZero => amount == 0;
}

/// Value object for URLs
@freezed
class Url with _$Url {
  const factory Url._(String value) = _Url;

  factory Url(String input) {
    if (_isValidUrl(input)) {
      return Url._(input.trim());
    }
    throw ArgumentError('Invalid URL: $input');
  }

  static bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }
}
