import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../core/domain/entities.dart';

part 'payment_method.freezed.dart';

/// Payment method types
enum PaymentMethodType {
  card,
  bankAccount,
  digitalWallet,
  crypto,
}

/// Card brands
enum CardBrand {
  visa,
  mastercard,
  amex,
  discover,
  unknown,
}

/// Payment method entity
@freezed
class PaymentMethod with _$PaymentMethod implements Entity {
  const factory PaymentMethod({
    required String id,
    required String userId,
    required PaymentMethodType type,
    required String displayName,
    required DateTime createdAt,
    DateTime? updatedAt,
    @Default(true) bool isActive,
    @Default(false) bool isDefault,
    String? last4,
    CardBrand? cardBrand,
    String? expiryMonth,
    String? expiryYear,
    String? bankName,
    String? accountType,
    String? stripePaymentMethodId,
    Map<String, dynamic>? metadata,
  }) = _PaymentMethod;

  const PaymentMethod._();

  /// Check if payment method is a card
  bool get isCard => type == PaymentMethodType.card;

  /// Check if payment method is a bank account
  bool get isBankAccount => type == PaymentMethodType.bankAccount;

  /// Check if payment method is expired (for cards)
  bool get isExpired {
    if (!isCard || expiryMonth == null || expiryYear == null) return false;
    
    final now = DateTime.now();
    final expiry = DateTime(
      int.parse('20$expiryYear'),
      int.parse(expiryMonth!),
    );
    
    return now.isAfter(expiry);
  }

  /// Check if payment method is expiring soon (within 2 months)
  bool get isExpiringSoon {
    if (!isCard || expiryMonth == null || expiryYear == null) return false;
    
    final now = DateTime.now();
    final expiry = DateTime(
      int.parse('20$expiryYear'),
      int.parse(expiryMonth!),
    );
    
    final twoMonthsFromNow = DateTime(now.year, now.month + 2, now.day);
    return expiry.isBefore(twoMonthsFromNow) && expiry.isAfter(now);
  }

  /// Get display text for the payment method
  String get displayText {
    switch (type) {
      case PaymentMethodType.card:
        final brand = cardBrand?.displayName ?? 'Card';
        final lastFour = last4 != null ? ' •••• $last4' : '';
        return '$brand$lastFour';
      case PaymentMethodType.bankAccount:
        final bank = bankName ?? 'Bank';
        final lastFour = last4 != null ? ' •••• $last4' : '';
        return '$bank$lastFour';
      case PaymentMethodType.digitalWallet:
        return displayName;
      case PaymentMethodType.crypto:
        return displayName;
    }
  }

  /// Get icon for the payment method
  String get icon {
    switch (type) {
      case PaymentMethodType.card:
        return cardBrand?.icon ?? 'credit_card';
      case PaymentMethodType.bankAccount:
        return 'account_balance';
      case PaymentMethodType.digitalWallet:
        return 'account_balance_wallet';
      case PaymentMethodType.crypto:
        return 'currency_bitcoin';
    }
  }

  /// Get status display
  String get statusDisplay {
    if (!isActive) return 'Inactive';
    if (isExpired) return 'Expired';
    if (isExpiringSoon) return 'Expiring Soon';
    return 'Active';
  }

  /// Get status color
  String get statusColor {
    if (!isActive || isExpired) return 'red';
    if (isExpiringSoon) return 'orange';
    return 'green';
  }

  /// Create a copy with default status
  PaymentMethod withDefault(bool isDefault) => copyWith(
    isDefault: isDefault,
    updatedAt: DateTime.now(),
  );

  /// Create a copy with active status
  PaymentMethod withActive(bool isActive) => copyWith(
    isActive: isActive,
    updatedAt: DateTime.now(),
  );

  /// Create a copy with updated metadata
  PaymentMethod withMetadata(Map<String, dynamic> metadata) => copyWith(
    metadata: metadata,
    updatedAt: DateTime.now(),
  );

  /// Check if payment method can be used for payments
  bool get canBeUsedForPayments => isActive && !isExpired;

  /// Check if payment method can be used for withdrawals
  bool get canBeUsedForWithdrawals => 
      isActive && 
      !isExpired && 
      (isBankAccount || type == PaymentMethodType.digitalWallet);

  /// Get expiry display text
  String? get expiryDisplay {
    if (expiryMonth == null || expiryYear == null) return null;
    return '$expiryMonth/$expiryYear';
  }

  /// Get masked display for security
  String get maskedDisplay {
    switch (type) {
      case PaymentMethodType.card:
        return '**** **** **** ${last4 ?? '****'}';
      case PaymentMethodType.bankAccount:
        return '****${last4 ?? '****'}';
      default:
        return displayName;
    }
  }
}

/// Extension for CardBrand
extension CardBrandExtension on CardBrand {
  String get displayName {
    switch (this) {
      case CardBrand.visa:
        return 'Visa';
      case CardBrand.mastercard:
        return 'Mastercard';
      case CardBrand.amex:
        return 'American Express';
      case CardBrand.discover:
        return 'Discover';
      case CardBrand.unknown:
        return 'Card';
    }
  }

  String get icon {
    switch (this) {
      case CardBrand.visa:
        return 'payment_visa';
      case CardBrand.mastercard:
        return 'payment_mastercard';
      case CardBrand.amex:
        return 'payment_amex';
      case CardBrand.discover:
        return 'payment_discover';
      case CardBrand.unknown:
        return 'credit_card';
    }
  }
}

/// Extension for PaymentMethodType
extension PaymentMethodTypeExtension on PaymentMethodType {
  String get displayName {
    switch (this) {
      case PaymentMethodType.card:
        return 'Credit/Debit Card';
      case PaymentMethodType.bankAccount:
        return 'Bank Account';
      case PaymentMethodType.digitalWallet:
        return 'Digital Wallet';
      case PaymentMethodType.crypto:
        return 'Cryptocurrency';
    }
  }
}
