// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wallet.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$Wallet {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  Money get balance => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  Money? get pendingBalance => throw _privateConstructorUsedError;
  Money? get availableBalance => throw _privateConstructorUsedError;
  DateTime? get lastTransactionAt => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Create a copy of Wallet
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WalletCopyWith<Wallet> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WalletCopyWith<$Res> {
  factory $WalletCopyWith(Wallet value, $Res Function(Wallet) then) =
      _$WalletCopyWithImpl<$Res, Wallet>;
  @useResult
  $Res call({
    String id,
    String userId,
    Money balance,
    DateTime createdAt,
    DateTime? updatedAt,
    bool isActive,
    String currency,
    Money? pendingBalance,
    Money? availableBalance,
    DateTime? lastTransactionAt,
    Map<String, dynamic>? metadata,
  });

  $MoneyCopyWith<$Res> get balance;
  $MoneyCopyWith<$Res>? get pendingBalance;
  $MoneyCopyWith<$Res>? get availableBalance;
}

/// @nodoc
class _$WalletCopyWithImpl<$Res, $Val extends Wallet>
    implements $WalletCopyWith<$Res> {
  _$WalletCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Wallet
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? balance = null,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? isActive = null,
    Object? currency = null,
    Object? pendingBalance = freezed,
    Object? availableBalance = freezed,
    Object? lastTransactionAt = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id:
                null == id
                    ? _value.id
                    : id // ignore: cast_nullable_to_non_nullable
                        as String,
            userId:
                null == userId
                    ? _value.userId
                    : userId // ignore: cast_nullable_to_non_nullable
                        as String,
            balance:
                null == balance
                    ? _value.balance
                    : balance // ignore: cast_nullable_to_non_nullable
                        as Money,
            createdAt:
                null == createdAt
                    ? _value.createdAt
                    : createdAt // ignore: cast_nullable_to_non_nullable
                        as DateTime,
            updatedAt:
                freezed == updatedAt
                    ? _value.updatedAt
                    : updatedAt // ignore: cast_nullable_to_non_nullable
                        as DateTime?,
            isActive:
                null == isActive
                    ? _value.isActive
                    : isActive // ignore: cast_nullable_to_non_nullable
                        as bool,
            currency:
                null == currency
                    ? _value.currency
                    : currency // ignore: cast_nullable_to_non_nullable
                        as String,
            pendingBalance:
                freezed == pendingBalance
                    ? _value.pendingBalance
                    : pendingBalance // ignore: cast_nullable_to_non_nullable
                        as Money?,
            availableBalance:
                freezed == availableBalance
                    ? _value.availableBalance
                    : availableBalance // ignore: cast_nullable_to_non_nullable
                        as Money?,
            lastTransactionAt:
                freezed == lastTransactionAt
                    ? _value.lastTransactionAt
                    : lastTransactionAt // ignore: cast_nullable_to_non_nullable
                        as DateTime?,
            metadata:
                freezed == metadata
                    ? _value.metadata
                    : metadata // ignore: cast_nullable_to_non_nullable
                        as Map<String, dynamic>?,
          )
          as $Val,
    );
  }

  /// Create a copy of Wallet
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MoneyCopyWith<$Res> get balance {
    return $MoneyCopyWith<$Res>(_value.balance, (value) {
      return _then(_value.copyWith(balance: value) as $Val);
    });
  }

  /// Create a copy of Wallet
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MoneyCopyWith<$Res>? get pendingBalance {
    if (_value.pendingBalance == null) {
      return null;
    }

    return $MoneyCopyWith<$Res>(_value.pendingBalance!, (value) {
      return _then(_value.copyWith(pendingBalance: value) as $Val);
    });
  }

  /// Create a copy of Wallet
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MoneyCopyWith<$Res>? get availableBalance {
    if (_value.availableBalance == null) {
      return null;
    }

    return $MoneyCopyWith<$Res>(_value.availableBalance!, (value) {
      return _then(_value.copyWith(availableBalance: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$WalletImplCopyWith<$Res> implements $WalletCopyWith<$Res> {
  factory _$$WalletImplCopyWith(
    _$WalletImpl value,
    $Res Function(_$WalletImpl) then,
  ) = __$$WalletImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    Money balance,
    DateTime createdAt,
    DateTime? updatedAt,
    bool isActive,
    String currency,
    Money? pendingBalance,
    Money? availableBalance,
    DateTime? lastTransactionAt,
    Map<String, dynamic>? metadata,
  });

  @override
  $MoneyCopyWith<$Res> get balance;
  @override
  $MoneyCopyWith<$Res>? get pendingBalance;
  @override
  $MoneyCopyWith<$Res>? get availableBalance;
}

/// @nodoc
class __$$WalletImplCopyWithImpl<$Res>
    extends _$WalletCopyWithImpl<$Res, _$WalletImpl>
    implements _$$WalletImplCopyWith<$Res> {
  __$$WalletImplCopyWithImpl(
    _$WalletImpl _value,
    $Res Function(_$WalletImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Wallet
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? balance = null,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? isActive = null,
    Object? currency = null,
    Object? pendingBalance = freezed,
    Object? availableBalance = freezed,
    Object? lastTransactionAt = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _$WalletImpl(
        id:
            null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                    as String,
        userId:
            null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                    as String,
        balance:
            null == balance
                ? _value.balance
                : balance // ignore: cast_nullable_to_non_nullable
                    as Money,
        createdAt:
            null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                    as DateTime,
        updatedAt:
            freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                    as DateTime?,
        isActive:
            null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                    as bool,
        currency:
            null == currency
                ? _value.currency
                : currency // ignore: cast_nullable_to_non_nullable
                    as String,
        pendingBalance:
            freezed == pendingBalance
                ? _value.pendingBalance
                : pendingBalance // ignore: cast_nullable_to_non_nullable
                    as Money?,
        availableBalance:
            freezed == availableBalance
                ? _value.availableBalance
                : availableBalance // ignore: cast_nullable_to_non_nullable
                    as Money?,
        lastTransactionAt:
            freezed == lastTransactionAt
                ? _value.lastTransactionAt
                : lastTransactionAt // ignore: cast_nullable_to_non_nullable
                    as DateTime?,
        metadata:
            freezed == metadata
                ? _value._metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                    as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc

class _$WalletImpl extends _Wallet {
  const _$WalletImpl({
    required this.id,
    required this.userId,
    required this.balance,
    required this.createdAt,
    this.updatedAt,
    this.isActive = true,
    this.currency = 'USD',
    this.pendingBalance,
    this.availableBalance,
    this.lastTransactionAt,
    final Map<String, dynamic>? metadata,
  }) : _metadata = metadata,
       super._();

  @override
  final String id;
  @override
  final String userId;
  @override
  final Money balance;
  @override
  final DateTime createdAt;
  @override
  final DateTime? updatedAt;
  @override
  @JsonKey()
  final bool isActive;
  @override
  @JsonKey()
  final String currency;
  @override
  final Money? pendingBalance;
  @override
  final Money? availableBalance;
  @override
  final DateTime? lastTransactionAt;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'Wallet(id: $id, userId: $userId, balance: $balance, createdAt: $createdAt, updatedAt: $updatedAt, isActive: $isActive, currency: $currency, pendingBalance: $pendingBalance, availableBalance: $availableBalance, lastTransactionAt: $lastTransactionAt, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WalletImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.pendingBalance, pendingBalance) ||
                other.pendingBalance == pendingBalance) &&
            (identical(other.availableBalance, availableBalance) ||
                other.availableBalance == availableBalance) &&
            (identical(other.lastTransactionAt, lastTransactionAt) ||
                other.lastTransactionAt == lastTransactionAt) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    balance,
    createdAt,
    updatedAt,
    isActive,
    currency,
    pendingBalance,
    availableBalance,
    lastTransactionAt,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of Wallet
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WalletImplCopyWith<_$WalletImpl> get copyWith =>
      __$$WalletImplCopyWithImpl<_$WalletImpl>(this, _$identity);
}

abstract class _Wallet extends Wallet {
  const factory _Wallet({
    required final String id,
    required final String userId,
    required final Money balance,
    required final DateTime createdAt,
    final DateTime? updatedAt,
    final bool isActive,
    final String currency,
    final Money? pendingBalance,
    final Money? availableBalance,
    final DateTime? lastTransactionAt,
    final Map<String, dynamic>? metadata,
  }) = _$WalletImpl;
  const _Wallet._() : super._();

  @override
  String get id;
  @override
  String get userId;
  @override
  Money get balance;
  @override
  DateTime get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  bool get isActive;
  @override
  String get currency;
  @override
  Money? get pendingBalance;
  @override
  Money? get availableBalance;
  @override
  DateTime? get lastTransactionAt;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of Wallet
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WalletImplCopyWith<_$WalletImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
