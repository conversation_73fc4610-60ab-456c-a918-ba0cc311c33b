import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../core/domain/entities.dart';
import 'transaction.dart';

part 'wallet.freezed.dart';

/// Wallet entity representing a user's wallet
@freezed
class Wallet with _$Wallet implements Entity {
  const factory Wallet({
    required String id,
    required String userId,
    required Money balance,
    required DateTime createdAt,
    DateTime? updatedAt,
    @Default(true) bool isActive,
    @Default('USD') String currency,
    Money? pendingBalance,
    Money? availableBalance,
    DateTime? lastTransactionAt,
    Map<String, dynamic>? metadata,
  }) = _Wallet;

  const Wallet._();

  /// Get effective available balance (balance minus pending amounts)
  Money get effectiveBalance {
    if (pendingBalance != null) {
      return balance - pendingBalance!;
    }
    return availableBalance ?? balance;
  }

  /// Check if wallet has sufficient funds for a transaction
  bool hasSufficientFunds(Money amount) {
    return effectiveBalance >= amount;
  }

  /// Check if wallet can perform withdrawals
  bool get canWithdraw => isActive && effectiveBalance.isPositive;

  /// Check if wallet can receive funds
  bool get canReceiveFunds => isActive;

  /// Get minimum withdrawal amount
  Money get minimumWithdrawal => Money(amount: 5.0, currency: currency);

  /// Check if amount meets minimum withdrawal requirement
  bool meetsMinimumWithdrawal(Money amount) {
    return amount >= minimumWithdrawal;
  }

  /// Create a copy with updated balance
  Wallet withUpdatedBalance(Money newBalance) => copyWith(
    balance: newBalance,
    updatedAt: DateTime.now(),
    lastTransactionAt: DateTime.now(),
  );

  /// Create a copy with added funds
  Wallet withAddedFunds(Money amount) => copyWith(
    balance: balance + amount,
    updatedAt: DateTime.now(),
    lastTransactionAt: DateTime.now(),
  );

  /// Create a copy with deducted funds
  Wallet withDeductedFunds(Money amount) => copyWith(
    balance: balance - amount,
    updatedAt: DateTime.now(),
    lastTransactionAt: DateTime.now(),
  );

  /// Create a copy with updated pending balance
  Wallet withPendingBalance(Money? pending) => copyWith(
    pendingBalance: pending,
    updatedAt: DateTime.now(),
  );

  /// Create a copy with updated available balance
  Wallet withAvailableBalance(Money? available) => copyWith(
    availableBalance: available,
    updatedAt: DateTime.now(),
  );

  /// Create a copy with deactivated status
  Wallet withDeactivated() => copyWith(
    isActive: false,
    updatedAt: DateTime.now(),
  );

  /// Create a copy with activated status
  Wallet withActivated() => copyWith(
    isActive: true,
    updatedAt: DateTime.now(),
  );

  /// Get wallet status display
  String get statusDisplay {
    if (!isActive) return 'Inactive';
    if (balance.isZero) return 'Empty';
    if (balance.isPositive) return 'Active';
    return 'Overdrawn';
  }

  /// Get wallet health indicator
  WalletHealth get health {
    if (!isActive) return WalletHealth.inactive;
    if (balance.amount < 0) return WalletHealth.overdrawn;
    if (balance.amount < 10) return WalletHealth.low;
    if (balance.amount < 50) return WalletHealth.moderate;
    return WalletHealth.healthy;
  }

  /// Check if wallet needs attention
  bool get needsAttention => 
      !isActive || 
      balance.amount < 0 || 
      (pendingBalance != null && pendingBalance!.amount > balance.amount * 0.5);

  /// Get formatted balance display
  String get balanceDisplay => balance.amount.toStringAsFixed(2);

  /// Get formatted effective balance display
  String get effectiveBalanceDisplay => effectiveBalance.amount.toStringAsFixed(2);

  /// Calculate balance change from a transaction
  Money calculateBalanceAfterTransaction(Transaction transaction) {
    return balance + transaction.effectiveAmount;
  }

  /// Check if transaction would overdraw the wallet
  bool wouldOverdraw(Transaction transaction) {
    final newBalance = calculateBalanceAfterTransaction(transaction);
    return newBalance.amount < 0;
  }

  /// Get time since last transaction
  Duration? get timeSinceLastTransaction {
    if (lastTransactionAt == null) return null;
    return DateTime.now().difference(lastTransactionAt!);
  }

  /// Check if wallet is dormant (no transactions in 30 days)
  bool get isDormant {
    final timeSince = timeSinceLastTransaction;
    return timeSince != null && timeSince.inDays > 30;
  }

  /// Get wallet age
  Duration get age => DateTime.now().difference(createdAt);

  /// Check if wallet is new (created within last 7 days)
  bool get isNew => age.inDays < 7;
}

/// Wallet health enumeration
enum WalletHealth {
  healthy,
  moderate,
  low,
  overdrawn,
  inactive,
}

/// Extension for WalletHealth display
extension WalletHealthExtension on WalletHealth {
  String get displayName {
    switch (this) {
      case WalletHealth.healthy:
        return 'Healthy';
      case WalletHealth.moderate:
        return 'Moderate';
      case WalletHealth.low:
        return 'Low Balance';
      case WalletHealth.overdrawn:
        return 'Overdrawn';
      case WalletHealth.inactive:
        return 'Inactive';
    }
  }

  String get color {
    switch (this) {
      case WalletHealth.healthy:
        return 'green';
      case WalletHealth.moderate:
        return 'yellow';
      case WalletHealth.low:
        return 'orange';
      case WalletHealth.overdrawn:
        return 'red';
      case WalletHealth.inactive:
        return 'grey';
    }
  }

  String get icon {
    switch (this) {
      case WalletHealth.healthy:
        return 'check_circle';
      case WalletHealth.moderate:
        return 'warning';
      case WalletHealth.low:
        return 'error_outline';
      case WalletHealth.overdrawn:
        return 'error';
      case WalletHealth.inactive:
        return 'block';
    }
  }
}
