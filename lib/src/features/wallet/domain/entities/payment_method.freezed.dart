// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_method.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$PaymentMethod {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  PaymentMethodType get type => throw _privateConstructorUsedError;
  String get displayName => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  bool get isDefault => throw _privateConstructorUsedError;
  String? get last4 => throw _privateConstructorUsedError;
  CardBrand? get cardBrand => throw _privateConstructorUsedError;
  String? get expiryMonth => throw _privateConstructorUsedError;
  String? get expiryYear => throw _privateConstructorUsedError;
  String? get bankName => throw _privateConstructorUsedError;
  String? get accountType => throw _privateConstructorUsedError;
  String? get stripePaymentMethodId => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Create a copy of PaymentMethod
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentMethodCopyWith<PaymentMethod> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentMethodCopyWith<$Res> {
  factory $PaymentMethodCopyWith(
    PaymentMethod value,
    $Res Function(PaymentMethod) then,
  ) = _$PaymentMethodCopyWithImpl<$Res, PaymentMethod>;
  @useResult
  $Res call({
    String id,
    String userId,
    PaymentMethodType type,
    String displayName,
    DateTime createdAt,
    DateTime? updatedAt,
    bool isActive,
    bool isDefault,
    String? last4,
    CardBrand? cardBrand,
    String? expiryMonth,
    String? expiryYear,
    String? bankName,
    String? accountType,
    String? stripePaymentMethodId,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$PaymentMethodCopyWithImpl<$Res, $Val extends PaymentMethod>
    implements $PaymentMethodCopyWith<$Res> {
  _$PaymentMethodCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentMethod
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? type = null,
    Object? displayName = null,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? isActive = null,
    Object? isDefault = null,
    Object? last4 = freezed,
    Object? cardBrand = freezed,
    Object? expiryMonth = freezed,
    Object? expiryYear = freezed,
    Object? bankName = freezed,
    Object? accountType = freezed,
    Object? stripePaymentMethodId = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id:
                null == id
                    ? _value.id
                    : id // ignore: cast_nullable_to_non_nullable
                        as String,
            userId:
                null == userId
                    ? _value.userId
                    : userId // ignore: cast_nullable_to_non_nullable
                        as String,
            type:
                null == type
                    ? _value.type
                    : type // ignore: cast_nullable_to_non_nullable
                        as PaymentMethodType,
            displayName:
                null == displayName
                    ? _value.displayName
                    : displayName // ignore: cast_nullable_to_non_nullable
                        as String,
            createdAt:
                null == createdAt
                    ? _value.createdAt
                    : createdAt // ignore: cast_nullable_to_non_nullable
                        as DateTime,
            updatedAt:
                freezed == updatedAt
                    ? _value.updatedAt
                    : updatedAt // ignore: cast_nullable_to_non_nullable
                        as DateTime?,
            isActive:
                null == isActive
                    ? _value.isActive
                    : isActive // ignore: cast_nullable_to_non_nullable
                        as bool,
            isDefault:
                null == isDefault
                    ? _value.isDefault
                    : isDefault // ignore: cast_nullable_to_non_nullable
                        as bool,
            last4:
                freezed == last4
                    ? _value.last4
                    : last4 // ignore: cast_nullable_to_non_nullable
                        as String?,
            cardBrand:
                freezed == cardBrand
                    ? _value.cardBrand
                    : cardBrand // ignore: cast_nullable_to_non_nullable
                        as CardBrand?,
            expiryMonth:
                freezed == expiryMonth
                    ? _value.expiryMonth
                    : expiryMonth // ignore: cast_nullable_to_non_nullable
                        as String?,
            expiryYear:
                freezed == expiryYear
                    ? _value.expiryYear
                    : expiryYear // ignore: cast_nullable_to_non_nullable
                        as String?,
            bankName:
                freezed == bankName
                    ? _value.bankName
                    : bankName // ignore: cast_nullable_to_non_nullable
                        as String?,
            accountType:
                freezed == accountType
                    ? _value.accountType
                    : accountType // ignore: cast_nullable_to_non_nullable
                        as String?,
            stripePaymentMethodId:
                freezed == stripePaymentMethodId
                    ? _value.stripePaymentMethodId
                    : stripePaymentMethodId // ignore: cast_nullable_to_non_nullable
                        as String?,
            metadata:
                freezed == metadata
                    ? _value.metadata
                    : metadata // ignore: cast_nullable_to_non_nullable
                        as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PaymentMethodImplCopyWith<$Res>
    implements $PaymentMethodCopyWith<$Res> {
  factory _$$PaymentMethodImplCopyWith(
    _$PaymentMethodImpl value,
    $Res Function(_$PaymentMethodImpl) then,
  ) = __$$PaymentMethodImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    PaymentMethodType type,
    String displayName,
    DateTime createdAt,
    DateTime? updatedAt,
    bool isActive,
    bool isDefault,
    String? last4,
    CardBrand? cardBrand,
    String? expiryMonth,
    String? expiryYear,
    String? bankName,
    String? accountType,
    String? stripePaymentMethodId,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$PaymentMethodImplCopyWithImpl<$Res>
    extends _$PaymentMethodCopyWithImpl<$Res, _$PaymentMethodImpl>
    implements _$$PaymentMethodImplCopyWith<$Res> {
  __$$PaymentMethodImplCopyWithImpl(
    _$PaymentMethodImpl _value,
    $Res Function(_$PaymentMethodImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PaymentMethod
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? type = null,
    Object? displayName = null,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? isActive = null,
    Object? isDefault = null,
    Object? last4 = freezed,
    Object? cardBrand = freezed,
    Object? expiryMonth = freezed,
    Object? expiryYear = freezed,
    Object? bankName = freezed,
    Object? accountType = freezed,
    Object? stripePaymentMethodId = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _$PaymentMethodImpl(
        id:
            null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                    as String,
        userId:
            null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                    as String,
        type:
            null == type
                ? _value.type
                : type // ignore: cast_nullable_to_non_nullable
                    as PaymentMethodType,
        displayName:
            null == displayName
                ? _value.displayName
                : displayName // ignore: cast_nullable_to_non_nullable
                    as String,
        createdAt:
            null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                    as DateTime,
        updatedAt:
            freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                    as DateTime?,
        isActive:
            null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                    as bool,
        isDefault:
            null == isDefault
                ? _value.isDefault
                : isDefault // ignore: cast_nullable_to_non_nullable
                    as bool,
        last4:
            freezed == last4
                ? _value.last4
                : last4 // ignore: cast_nullable_to_non_nullable
                    as String?,
        cardBrand:
            freezed == cardBrand
                ? _value.cardBrand
                : cardBrand // ignore: cast_nullable_to_non_nullable
                    as CardBrand?,
        expiryMonth:
            freezed == expiryMonth
                ? _value.expiryMonth
                : expiryMonth // ignore: cast_nullable_to_non_nullable
                    as String?,
        expiryYear:
            freezed == expiryYear
                ? _value.expiryYear
                : expiryYear // ignore: cast_nullable_to_non_nullable
                    as String?,
        bankName:
            freezed == bankName
                ? _value.bankName
                : bankName // ignore: cast_nullable_to_non_nullable
                    as String?,
        accountType:
            freezed == accountType
                ? _value.accountType
                : accountType // ignore: cast_nullable_to_non_nullable
                    as String?,
        stripePaymentMethodId:
            freezed == stripePaymentMethodId
                ? _value.stripePaymentMethodId
                : stripePaymentMethodId // ignore: cast_nullable_to_non_nullable
                    as String?,
        metadata:
            freezed == metadata
                ? _value._metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                    as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc

class _$PaymentMethodImpl extends _PaymentMethod {
  const _$PaymentMethodImpl({
    required this.id,
    required this.userId,
    required this.type,
    required this.displayName,
    required this.createdAt,
    this.updatedAt,
    this.isActive = true,
    this.isDefault = false,
    this.last4,
    this.cardBrand,
    this.expiryMonth,
    this.expiryYear,
    this.bankName,
    this.accountType,
    this.stripePaymentMethodId,
    final Map<String, dynamic>? metadata,
  }) : _metadata = metadata,
       super._();

  @override
  final String id;
  @override
  final String userId;
  @override
  final PaymentMethodType type;
  @override
  final String displayName;
  @override
  final DateTime createdAt;
  @override
  final DateTime? updatedAt;
  @override
  @JsonKey()
  final bool isActive;
  @override
  @JsonKey()
  final bool isDefault;
  @override
  final String? last4;
  @override
  final CardBrand? cardBrand;
  @override
  final String? expiryMonth;
  @override
  final String? expiryYear;
  @override
  final String? bankName;
  @override
  final String? accountType;
  @override
  final String? stripePaymentMethodId;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'PaymentMethod(id: $id, userId: $userId, type: $type, displayName: $displayName, createdAt: $createdAt, updatedAt: $updatedAt, isActive: $isActive, isDefault: $isDefault, last4: $last4, cardBrand: $cardBrand, expiryMonth: $expiryMonth, expiryYear: $expiryYear, bankName: $bankName, accountType: $accountType, stripePaymentMethodId: $stripePaymentMethodId, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentMethodImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isDefault, isDefault) ||
                other.isDefault == isDefault) &&
            (identical(other.last4, last4) || other.last4 == last4) &&
            (identical(other.cardBrand, cardBrand) ||
                other.cardBrand == cardBrand) &&
            (identical(other.expiryMonth, expiryMonth) ||
                other.expiryMonth == expiryMonth) &&
            (identical(other.expiryYear, expiryYear) ||
                other.expiryYear == expiryYear) &&
            (identical(other.bankName, bankName) ||
                other.bankName == bankName) &&
            (identical(other.accountType, accountType) ||
                other.accountType == accountType) &&
            (identical(other.stripePaymentMethodId, stripePaymentMethodId) ||
                other.stripePaymentMethodId == stripePaymentMethodId) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    type,
    displayName,
    createdAt,
    updatedAt,
    isActive,
    isDefault,
    last4,
    cardBrand,
    expiryMonth,
    expiryYear,
    bankName,
    accountType,
    stripePaymentMethodId,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of PaymentMethod
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentMethodImplCopyWith<_$PaymentMethodImpl> get copyWith =>
      __$$PaymentMethodImplCopyWithImpl<_$PaymentMethodImpl>(this, _$identity);
}

abstract class _PaymentMethod extends PaymentMethod {
  const factory _PaymentMethod({
    required final String id,
    required final String userId,
    required final PaymentMethodType type,
    required final String displayName,
    required final DateTime createdAt,
    final DateTime? updatedAt,
    final bool isActive,
    final bool isDefault,
    final String? last4,
    final CardBrand? cardBrand,
    final String? expiryMonth,
    final String? expiryYear,
    final String? bankName,
    final String? accountType,
    final String? stripePaymentMethodId,
    final Map<String, dynamic>? metadata,
  }) = _$PaymentMethodImpl;
  const _PaymentMethod._() : super._();

  @override
  String get id;
  @override
  String get userId;
  @override
  PaymentMethodType get type;
  @override
  String get displayName;
  @override
  DateTime get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  bool get isActive;
  @override
  bool get isDefault;
  @override
  String? get last4;
  @override
  CardBrand? get cardBrand;
  @override
  String? get expiryMonth;
  @override
  String? get expiryYear;
  @override
  String? get bankName;
  @override
  String? get accountType;
  @override
  String? get stripePaymentMethodId;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of PaymentMethod
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentMethodImplCopyWith<_$PaymentMethodImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
