import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../core/domain/entities.dart';

part 'transaction.freezed.dart';

/// Transaction types enumeration
enum TransactionType {
  credit,
  debit,
  withdrawal,
  postPayment,
  refund,
  fee,
}

/// Transaction status enumeration
enum TransactionStatus {
  pending,
  completed,
  failed,
  cancelled,
  processing,
}

/// Transaction entity representing a wallet transaction
@freezed
class Transaction with _$Transaction implements Entity {
  const factory Transaction({
    required String id,
    required TransactionType type,
    required Money amount,
    required String description,
    required DateTime createdAt,
    required TransactionStatus status,
    DateTime? updatedAt,
    DateTime? completedAt,
    String? postId,
    String? paymentIntentId,
    String? withdrawalId,
    String? failureReason,
    Map<String, dynamic>? metadata,
  }) = _Transaction;

  const Transaction._();

  /// Check if transaction is completed
  bool get isCompleted => status == TransactionStatus.completed;

  /// Check if transaction is pending
  bool get isPending => status == TransactionStatus.pending;

  /// Check if transaction is failed
  bool get isFailed => status == TransactionStatus.failed;

  /// Check if transaction is processing
  bool get isProcessing => status == TransactionStatus.processing;

  /// Check if transaction is cancelled
  bool get isCancelled => status == TransactionStatus.cancelled;

  /// Check if transaction is a credit (adds to balance)
  bool get isCredit => type == TransactionType.credit || type == TransactionType.refund;

  /// Check if transaction is a debit (subtracts from balance)
  bool get isDebit => 
      type == TransactionType.debit || 
      type == TransactionType.withdrawal || 
      type == TransactionType.postPayment ||
      type == TransactionType.fee;

  /// Get the effective amount for balance calculation
  /// Credits are positive, debits are negative
  Money get effectiveAmount => isCredit ? amount : Money(amount: -amount.amount, currency: amount.currency);

  /// Get display color based on transaction type
  String get displayColor {
    if (isFailed || isCancelled) return 'red';
    if (isPending || isProcessing) return 'orange';
    return isCredit ? 'green' : 'red';
  }

  /// Get display icon based on transaction type
  String get displayIcon {
    switch (type) {
      case TransactionType.credit:
        return 'add_circle';
      case TransactionType.debit:
        return 'remove_circle';
      case TransactionType.withdrawal:
        return 'account_balance';
      case TransactionType.postPayment:
        return 'post_add';
      case TransactionType.refund:
        return 'refresh';
      case TransactionType.fee:
        return 'receipt';
    }
  }

  /// Get user-friendly transaction type name
  String get displayTypeName {
    switch (type) {
      case TransactionType.credit:
        return 'Added Funds';
      case TransactionType.debit:
        return 'Payment';
      case TransactionType.withdrawal:
        return 'Withdrawal';
      case TransactionType.postPayment:
        return 'Post Payment';
      case TransactionType.refund:
        return 'Refund';
      case TransactionType.fee:
        return 'Fee';
    }
  }

  /// Get user-friendly status name
  String get displayStatusName {
    switch (status) {
      case TransactionStatus.pending:
        return 'Pending';
      case TransactionStatus.completed:
        return 'Completed';
      case TransactionStatus.failed:
        return 'Failed';
      case TransactionStatus.cancelled:
        return 'Cancelled';
      case TransactionStatus.processing:
        return 'Processing';
    }
  }

  /// Create a copy with completed status
  Transaction withCompleted({DateTime? completedAt}) => copyWith(
    status: TransactionStatus.completed,
    completedAt: completedAt ?? DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// Create a copy with failed status
  Transaction withFailed(String reason) => copyWith(
    status: TransactionStatus.failed,
    failureReason: reason,
    updatedAt: DateTime.now(),
  );

  /// Create a copy with cancelled status
  Transaction withCancelled() => copyWith(
    status: TransactionStatus.cancelled,
    updatedAt: DateTime.now(),
  );

  /// Create a copy with processing status
  Transaction withProcessing() => copyWith(
    status: TransactionStatus.processing,
    updatedAt: DateTime.now(),
  );

  /// Check if transaction can be cancelled
  bool get canBeCancelled => 
      status == TransactionStatus.pending && 
      type == TransactionType.withdrawal;

  /// Check if transaction can be retried
  bool get canBeRetried => 
      status == TransactionStatus.failed && 
      (type == TransactionType.credit || type == TransactionType.withdrawal);

  /// Get time since transaction was created
  Duration get timeSinceCreated => DateTime.now().difference(createdAt);

  /// Check if transaction is recent (within last 24 hours)
  bool get isRecent => timeSinceCreated.inHours < 24;

  /// Get formatted time display
  String get timeDisplay {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }
}
