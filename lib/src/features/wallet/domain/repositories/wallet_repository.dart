import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/domain/entities.dart';
import '../entities/wallet.dart';
import '../entities/transaction.dart';
import '../entities/payment_method.dart';

/// Abstract repository interface for wallet operations
abstract class WalletRepository {
  /// Stream of wallet changes
  Stream<Wallet?> watchWallet(String userId);

  /// Get wallet for user
  Future<Either<Failure, Wallet?>> getWallet(String userId);

  /// Create wallet for user
  Future<Either<Failure, Wallet>> createWallet({
    required String userId,
    String currency = 'USD',
  });

  /// Update wallet balance
  Future<Either<Failure, Wallet>> updateBalance({
    required String userId,
    required Money newBalance,
  });

  /// Add funds to wallet
  Future<Either<Failure, Transaction>> addFunds({
    required String userId,
    required Money amount,
    required String paymentMethodId,
    String? description,
  });

  /// Deduct funds from wallet
  Future<Either<Failure, Transaction>> deductFunds({
    required String userId,
    required Money amount,
    required String description,
    String? postId,
    TransactionType type = TransactionType.debit,
  });

  /// Withdraw funds from wallet
  Future<Either<Failure, Transaction>> withdrawFunds({
    required String userId,
    required Money amount,
    required String paymentMethodId,
    String? description,
  });

  /// Process refund
  Future<Either<Failure, Transaction>> processRefund({
    required String userId,
    required String originalTransactionId,
    required Money amount,
    required String reason,
  });

  /// Get transaction by ID
  Future<Either<Failure, Transaction?>> getTransaction({
    required String userId,
    required String transactionId,
  });

  /// Get transaction history
  Future<Either<Failure, List<Transaction>>> getTransactionHistory({
    required String userId,
    int limit = 50,
    String? lastTransactionId,
    List<TransactionType>? types,
    List<TransactionStatus>? statuses,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get transactions by type
  Future<Either<Failure, List<Transaction>>> getTransactionsByType({
    required String userId,
    required TransactionType type,
    int limit = 50,
  });

  /// Get pending transactions
  Future<Either<Failure, List<Transaction>>> getPendingTransactions({
    required String userId,
  });

  /// Update transaction status
  Future<Either<Failure, Transaction>> updateTransactionStatus({
    required String userId,
    required String transactionId,
    required TransactionStatus status,
    String? failureReason,
  });

  /// Cancel transaction
  Future<Either<Failure, Transaction>> cancelTransaction({
    required String userId,
    required String transactionId,
    required String reason,
  });

  /// Get wallet statistics
  Future<Either<Failure, WalletStatistics>> getWalletStatistics({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get total earnings
  Future<Either<Failure, Money>> getTotalEarnings({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get total spent
  Future<Either<Failure, Money>> getTotalSpent({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Check if user has sufficient funds
  Future<Either<Failure, bool>> hasSufficientFunds({
    required String userId,
    required Money amount,
  });

  /// Get payment methods
  Future<Either<Failure, List<PaymentMethod>>> getPaymentMethods({
    required String userId,
  });

  /// Add payment method
  Future<Either<Failure, PaymentMethod>> addPaymentMethod({
    required String userId,
    required PaymentMethodType type,
    required String displayName,
    required Map<String, dynamic> paymentData,
  });

  /// Remove payment method
  Future<Either<Failure, Unit>> removePaymentMethod({
    required String userId,
    required String paymentMethodId,
  });

  /// Set default payment method
  Future<Either<Failure, Unit>> setDefaultPaymentMethod({
    required String userId,
    required String paymentMethodId,
  });

  /// Process payment intent
  Future<Either<Failure, String>> createPaymentIntent({
    required Money amount,
    required String currency,
    Map<String, dynamic>? metadata,
  });

  /// Confirm payment
  Future<Either<Failure, bool>> confirmPayment({
    required String paymentIntentId,
    required String paymentMethodId,
  });

  /// Process withdrawal
  Future<Either<Failure, Map<String, dynamic>>> processWithdrawal({
    required Money amount,
    required String paymentMethodId,
  });

  /// Get exchange rates (for multi-currency support)
  Future<Either<Failure, Map<String, double>>> getExchangeRates({
    required String baseCurrency,
    required List<String> targetCurrencies,
  });

  /// Convert currency
  Future<Either<Failure, Money>> convertCurrency({
    required Money amount,
    required String targetCurrency,
  });

  /// Validate transaction
  Future<Either<Failure, bool>> validateTransaction({
    required String userId,
    required TransactionType type,
    required Money amount,
    String? paymentMethodId,
  });

  /// Get transaction fees
  Future<Either<Failure, Money>> calculateTransactionFees({
    required TransactionType type,
    required Money amount,
    String? paymentMethodId,
  });

  /// Sync wallet with external services
  Future<Either<Failure, Unit>> syncWallet({
    required String userId,
  });
}

/// Wallet statistics data class
class WalletStatistics {
  final Money totalEarnings;
  final Money totalSpent;
  final Money netBalance;
  final int totalTransactions;
  final int completedTransactions;
  final int pendingTransactions;
  final int failedTransactions;
  final Money averageTransactionAmount;
  final DateTime? lastTransactionDate;
  final Map<TransactionType, int> transactionsByType;
  final Map<TransactionStatus, int> transactionsByStatus;

  const WalletStatistics({
    required this.totalEarnings,
    required this.totalSpent,
    required this.netBalance,
    required this.totalTransactions,
    required this.completedTransactions,
    required this.pendingTransactions,
    required this.failedTransactions,
    required this.averageTransactionAmount,
    this.lastTransactionDate,
    required this.transactionsByType,
    required this.transactionsByStatus,
  });

  double get successRate => 
      totalTransactions > 0 ? completedTransactions / totalTransactions : 0.0;

  bool get hasRecentActivity => 
      lastTransactionDate != null && 
      DateTime.now().difference(lastTransactionDate!).inDays < 7;
}
