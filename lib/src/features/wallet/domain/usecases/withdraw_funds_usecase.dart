import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/domain/entities.dart';
import '../entities/transaction.dart';
import '../entities/payment_method.dart';
import '../repositories/wallet_repository.dart';

/// Use case for withdrawing funds from wallet
class WithdrawFundsUseCase {
  final WalletRepository _repository;

  const WithdrawFundsUseCase(this._repository);

  /// Execute withdraw funds operation
  Future<Either<Failure, Transaction>> call(WithdrawFundsParams params) async {
    // Validate input parameters
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      return Left(validationResult);
    }

    try {
      // Check if user has sufficient funds
      final sufficientFundsResult = await _repository.hasSufficientFunds(
        userId: params.userId,
        amount: params.amount,
      );

      final hasSufficientFunds = sufficientFundsResult.fold(
        (failure) => false,
        (sufficient) => sufficient,
      );

      if (!hasSufficientFunds) {
        return const Left(Failure.validation(
          message: 'Insufficient funds for withdrawal',
          field: 'amount',
        ));
      }

      // Check if payment method exists and can be used for withdrawals
      final paymentMethodsResult = await _repository.getPaymentMethods(
        userId: params.userId,
      );

      final paymentMethods = paymentMethodsResult.fold(
        (failure) => <PaymentMethod>[],
        (methods) => methods,
      );

      final paymentMethod = paymentMethods
          .where((pm) => pm.id == params.paymentMethodId)
          .firstOrNull;

      if (paymentMethod == null) {
        return const Left(Failure.validation(
          message: 'Payment method not found',
          field: 'paymentMethodId',
        ));
      }

      if (!paymentMethod.canBeUsedForWithdrawals) {
        return const Left(Failure.validation(
          message: 'Payment method cannot be used for withdrawals',
          field: 'paymentMethodId',
        ));
      }

      // Calculate withdrawal fees
      final feesResult = await _repository.calculateTransactionFees(
        type: TransactionType.withdrawal,
        amount: params.amount,
        paymentMethodId: params.paymentMethodId,
      );

      final fees = feesResult.fold(
        (failure) => Money.zero(),
        (fees) => fees,
      );

      // Check if user has sufficient funds including fees
      final totalAmount = params.amount + fees;
      final sufficientForFeesResult = await _repository.hasSufficientFunds(
        userId: params.userId,
        amount: totalAmount,
      );

      final hasSufficientForFees = sufficientForFeesResult.fold(
        (failure) => false,
        (sufficient) => sufficient,
      );

      if (!hasSufficientForFees) {
        return Left(Failure.validation(
          message: 'Insufficient funds for withdrawal including fees (${fees.amount.toStringAsFixed(2)})',
          field: 'amount',
        ));
      }

      // Validate transaction
      final validationResult = await _repository.validateTransaction(
        userId: params.userId,
        type: TransactionType.withdrawal,
        amount: params.amount,
        paymentMethodId: params.paymentMethodId,
      );

      final isValid = validationResult.fold(
        (failure) => false,
        (valid) => valid,
      );

      if (!isValid) {
        return const Left(Failure.validation(
          message: 'Withdrawal validation failed',
        ));
      }

      // Process the withdrawal
      final result = await _repository.withdrawFunds(
        userId: params.userId,
        amount: params.amount,
        paymentMethodId: params.paymentMethodId,
        description: params.description ?? 'Withdrawal',
      );

      return result.fold(
        (failure) => Left(failure),
        (transaction) async {
          // If there are fees, create a separate fee transaction
          if (fees.isPositive) {
            await _repository.deductFunds(
              userId: params.userId,
              amount: fees,
              description: 'Withdrawal fee',
              type: TransactionType.fee,
            );
          }

          return Right(transaction);
        },
      );
    } catch (e) {
      return Left(Failure.unknown(
        message: 'Unexpected error while processing withdrawal',
        exception: e,
      ));
    }
  }

  /// Validate withdraw funds parameters
  Failure? _validateParams(WithdrawFundsParams params) {
    if (params.userId.isEmpty) {
      return const Failure.validation(
        message: 'User ID cannot be empty',
        field: 'userId',
      );
    }

    if (params.paymentMethodId.isEmpty) {
      return const Failure.validation(
        message: 'Payment method ID cannot be empty',
        field: 'paymentMethodId',
      );
    }

    if (!params.amount.isPositive) {
      return const Failure.validation(
        message: 'Amount must be positive',
        field: 'amount',
      );
    }

    // Check minimum withdrawal amount ($5.00)
    if (params.amount.amount < 5.0) {
      return const Failure.validation(
        message: 'Minimum withdrawal amount is \$5.00',
        field: 'amount',
      );
    }

    // Check maximum withdrawal amount ($5,000)
    if (params.amount.amount > 5000.0) {
      return const Failure.validation(
        message: 'Maximum withdrawal amount is \$5,000',
        field: 'amount',
      );
    }

    return null;
  }
}

/// Parameters for withdraw funds use case
class WithdrawFundsParams {
  final String userId;
  final Money amount;
  final String paymentMethodId;
  final String? description;

  const WithdrawFundsParams({
    required this.userId,
    required this.amount,
    required this.paymentMethodId,
    this.description,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WithdrawFundsParams &&
          runtimeType == other.runtimeType &&
          userId == other.userId &&
          amount == other.amount &&
          paymentMethodId == other.paymentMethodId &&
          description == other.description;

  @override
  int get hashCode =>
      userId.hashCode ^
      amount.hashCode ^
      paymentMethodId.hashCode ^
      description.hashCode;

  @override
  String toString() => 'WithdrawFundsParams('
      'userId: $userId, '
      'amount: $amount, '
      'paymentMethodId: $paymentMethodId, '
      'description: $description)';
}

/// Extension to add firstOrNull method if not available
extension IterableExtension<T> on Iterable<T> {
  T? get firstOrNull {
    final iterator = this.iterator;
    if (iterator.moveNext()) {
      return iterator.current;
    }
    return null;
  }
}
