import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/transaction.dart';
import '../repositories/wallet_repository.dart';

/// Use case for getting transaction history
class GetTransactionHistoryUseCase {
  final WalletRepository _repository;

  const GetTransactionHistoryUseCase(this._repository);

  /// Execute get transaction history operation
  Future<Either<Failure, List<Transaction>>> call(
    GetTransactionHistoryParams params,
  ) async {
    // Validate input parameters
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      return Left(validationResult);
    }

    try {
      return await _repository.getTransactionHistory(
        userId: params.userId,
        limit: params.limit,
        lastTransactionId: params.lastTransactionId,
        types: params.types,
        statuses: params.statuses,
        startDate: params.startDate,
        endDate: params.endDate,
      );
    } catch (e) {
      return Left(Failure.unknown(
        message: 'Unexpected error while getting transaction history',
        exception: e,
      ));
    }
  }

  /// Get transactions by type
  Future<Either<Failure, List<Transaction>>> getByType({
    required String userId,
    required TransactionType type,
    int limit = 50,
  }) async {
    if (userId.isEmpty) {
      return const Left(Failure.validation(
        message: 'User ID cannot be empty',
        field: 'userId',
      ));
    }

    try {
      return await _repository.getTransactionsByType(
        userId: userId,
        type: type,
        limit: limit,
      );
    } catch (e) {
      return Left(Failure.unknown(
        message: 'Unexpected error while getting transactions by type',
        exception: e,
      ));
    }
  }

  /// Get pending transactions
  Future<Either<Failure, List<Transaction>>> getPending({
    required String userId,
  }) async {
    if (userId.isEmpty) {
      return const Left(Failure.validation(
        message: 'User ID cannot be empty',
        field: 'userId',
      ));
    }

    try {
      return await _repository.getPendingTransactions(userId: userId);
    } catch (e) {
      return Left(Failure.unknown(
        message: 'Unexpected error while getting pending transactions',
        exception: e,
      ));
    }
  }

  /// Validate parameters
  Failure? _validateParams(GetTransactionHistoryParams params) {
    if (params.userId.isEmpty) {
      return const Failure.validation(
        message: 'User ID cannot be empty',
        field: 'userId',
      );
    }

    if (params.limit <= 0) {
      return const Failure.validation(
        message: 'Limit must be positive',
        field: 'limit',
      );
    }

    if (params.limit > 1000) {
      return const Failure.validation(
        message: 'Limit cannot exceed 1000',
        field: 'limit',
      );
    }

    if (params.startDate != null && 
        params.endDate != null && 
        params.startDate!.isAfter(params.endDate!)) {
      return const Failure.validation(
        message: 'Start date cannot be after end date',
        field: 'dateRange',
      );
    }

    return null;
  }
}

/// Parameters for get transaction history use case
class GetTransactionHistoryParams {
  final String userId;
  final int limit;
  final String? lastTransactionId;
  final List<TransactionType>? types;
  final List<TransactionStatus>? statuses;
  final DateTime? startDate;
  final DateTime? endDate;

  const GetTransactionHistoryParams({
    required this.userId,
    this.limit = 50,
    this.lastTransactionId,
    this.types,
    this.statuses,
    this.startDate,
    this.endDate,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GetTransactionHistoryParams &&
          runtimeType == other.runtimeType &&
          userId == other.userId &&
          limit == other.limit &&
          lastTransactionId == other.lastTransactionId &&
          _listEquals(types, other.types) &&
          _listEquals(statuses, other.statuses) &&
          startDate == other.startDate &&
          endDate == other.endDate;

  @override
  int get hashCode =>
      userId.hashCode ^
      limit.hashCode ^
      lastTransactionId.hashCode ^
      types.hashCode ^
      statuses.hashCode ^
      startDate.hashCode ^
      endDate.hashCode;

  @override
  String toString() => 'GetTransactionHistoryParams('
      'userId: $userId, '
      'limit: $limit, '
      'lastTransactionId: $lastTransactionId, '
      'types: $types, '
      'statuses: $statuses, '
      'startDate: $startDate, '
      'endDate: $endDate)';

  /// Helper method to compare lists
  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }
}
