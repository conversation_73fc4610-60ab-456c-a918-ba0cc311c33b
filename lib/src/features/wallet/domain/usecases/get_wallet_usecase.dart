import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/wallet.dart';
import '../repositories/wallet_repository.dart';

/// Use case for getting wallet information
class GetWalletUseCase {
  final WalletRepository _repository;

  const GetWalletUseCase(this._repository);

  /// Execute get wallet operation
  Future<Either<Failure, Wallet?>> call(String userId) async {
    if (userId.isEmpty) {
      return const Left(Failure.validation(
        message: 'User ID cannot be empty',
        field: 'userId',
      ));
    }

    try {
      return await _repository.getWallet(userId);
    } catch (e) {
      return Left(Failure.unknown(
        message: 'Unexpected error while getting wallet',
        exception: e,
      ));
    }
  }

  /// Get wallet stream for real-time updates
  Stream<Wallet?> watchWallet(String userId) {
    if (userId.isEmpty) {
      return Stream.error(const Failure.validation(
        message: 'User ID cannot be empty',
        field: 'userId',
      ));
    }

    return _repository.watchWallet(userId);
  }
}
