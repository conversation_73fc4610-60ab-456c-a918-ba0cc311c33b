import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/domain/entities.dart';
import '../entities/transaction.dart';
import '../entities/payment_method.dart';
import '../repositories/wallet_repository.dart';

/// Use case for adding funds to wallet
class AddFundsUseCase {
  final WalletRepository _repository;

  const AddFundsUseCase(this._repository);

  /// Execute add funds operation
  Future<Either<Failure, Transaction>> call(AddFundsParams params) async {
    // Validate input parameters
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      return Left(validationResult);
    }

    try {
      // Check if payment method exists and is valid
      final paymentMethodsResult = await _repository.getPaymentMethods(
        userId: params.userId,
      );

      final paymentMethods = paymentMethodsResult.fold(
        (failure) => <PaymentMethod>[],
        (methods) => methods,
      );

      final paymentMethod =
          paymentMethods
              .where((pm) => pm.id == params.paymentMethodId)
              .firstOrNull;

      if (paymentMethod == null) {
        return const Left(
          Failure.validation(
            message: 'Payment method not found',
            field: 'paymentMethodId',
          ),
        );
      }

      if (!paymentMethod.canBeUsedForPayments) {
        return const Left(
          Failure.validation(
            message: 'Payment method cannot be used for payments',
            field: 'paymentMethodId',
          ),
        );
      }

      // Calculate transaction fees
      final feesResult = await _repository.calculateTransactionFees(
        type: TransactionType.credit,
        amount: params.amount,
        paymentMethodId: params.paymentMethodId,
      );

      final fees = feesResult.fold((failure) => Money.zero(), (fees) => fees);

      // Validate transaction
      final validationResult = await _repository.validateTransaction(
        userId: params.userId,
        type: TransactionType.credit,
        amount: params.amount,
        paymentMethodId: params.paymentMethodId,
      );

      final isValid = validationResult.fold(
        (failure) => false,
        (valid) => valid,
      );

      if (!isValid) {
        return const Left(
          Failure.validation(message: 'Transaction validation failed'),
        );
      }

      // Process the add funds transaction
      final result = await _repository.addFunds(
        userId: params.userId,
        amount: params.amount,
        paymentMethodId: params.paymentMethodId,
        description: params.description ?? 'Added funds',
      );

      return result.fold((failure) => Left(failure), (transaction) async {
        // If there are fees, create a separate fee transaction
        if (fees.isPositive) {
          await _repository.deductFunds(
            userId: params.userId,
            amount: fees,
            description: 'Transaction fee',
            type: TransactionType.fee,
          );
        }

        return Right(transaction);
      });
    } catch (e) {
      return Left(
        Failure.unknown(
          message: 'Unexpected error while adding funds',
          exception: e,
        ),
      );
    }
  }

  /// Validate add funds parameters
  Failure? _validateParams(AddFundsParams params) {
    if (params.userId.isEmpty) {
      return const Failure.validation(
        message: 'User ID cannot be empty',
        field: 'userId',
      );
    }

    if (params.paymentMethodId.isEmpty) {
      return const Failure.validation(
        message: 'Payment method ID cannot be empty',
        field: 'paymentMethodId',
      );
    }

    if (!params.amount.isPositive) {
      return const Failure.validation(
        message: 'Amount must be positive',
        field: 'amount',
      );
    }

    // Check minimum amount (e.g., $1.00)
    if (params.amount.amount < 1.0) {
      return const Failure.validation(
        message: 'Minimum amount is \$1.00',
        field: 'amount',
      );
    }

    // Check maximum amount (e.g., $10,000)
    if (params.amount.amount > 10000.0) {
      return const Failure.validation(
        message: 'Maximum amount is \$10,000',
        field: 'amount',
      );
    }

    return null;
  }
}

/// Parameters for add funds use case
class AddFundsParams {
  final String userId;
  final Money amount;
  final String paymentMethodId;
  final String? description;

  const AddFundsParams({
    required this.userId,
    required this.amount,
    required this.paymentMethodId,
    this.description,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AddFundsParams &&
          runtimeType == other.runtimeType &&
          userId == other.userId &&
          amount == other.amount &&
          paymentMethodId == other.paymentMethodId &&
          description == other.description;

  @override
  int get hashCode =>
      userId.hashCode ^
      amount.hashCode ^
      paymentMethodId.hashCode ^
      description.hashCode;

  @override
  String toString() =>
      'AddFundsParams('
      'userId: $userId, '
      'amount: $amount, '
      'paymentMethodId: $paymentMethodId, '
      'description: $description)';
}

/// Extension to add firstOrNull method if not available
extension IterableExtension<T> on Iterable<T> {
  T? get firstOrNull {
    final iterator = this.iterator;
    if (iterator.moveNext()) {
      return iterator.current;
    }
    return null;
  }
}
