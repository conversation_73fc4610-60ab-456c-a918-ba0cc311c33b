import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/core_providers.dart';
import '../../../core/domain/entities.dart';
import '../../../../services/payment_service.dart';
import '../data/repositories/firebase_wallet_repository.dart';
import '../data/services/payment_service_adapter.dart';
import '../domain/entities/transaction.dart';
import '../domain/repositories/wallet_repository.dart';
import '../domain/usecases/add_funds_usecase.dart';
import '../domain/usecases/withdraw_funds_usecase.dart';
import '../domain/usecases/get_wallet_usecase.dart';
import '../domain/usecases/get_transaction_history_usecase.dart';
import 'wallet_notifier.dart';
import 'wallet_state.dart';

/// Payment service adapter provider
final paymentServiceAdapterProvider = Provider<PaymentServiceAdapter>((ref) {
  // Create legacy PaymentService instance
  final paymentService = PaymentService();
  return PaymentServiceAdapter(paymentService);
});

/// Wallet repository provider
final walletRepositoryProvider = Provider<WalletRepository>((ref) {
  final firestore = ref.watch(firestoreProvider);
  final paymentServiceAdapter = ref.watch(paymentServiceAdapterProvider);

  return FirebaseWalletRepository(
    firestore: firestore,
    paymentService: paymentServiceAdapter,
  );
});

/// Get wallet use case provider
final getWalletUseCaseProvider = Provider<GetWalletUseCase>((ref) {
  final repository = ref.watch(walletRepositoryProvider);
  return GetWalletUseCase(repository);
});

/// Add funds use case provider
final addFundsUseCaseProvider = Provider<AddFundsUseCase>((ref) {
  final repository = ref.watch(walletRepositoryProvider);
  return AddFundsUseCase(repository);
});

/// Withdraw funds use case provider
final withdrawFundsUseCaseProvider = Provider<WithdrawFundsUseCase>((ref) {
  final repository = ref.watch(walletRepositoryProvider);
  return WithdrawFundsUseCase(repository);
});

/// Get transaction history use case provider
final getTransactionHistoryUseCaseProvider =
    Provider<GetTransactionHistoryUseCase>((ref) {
      final repository = ref.watch(walletRepositoryProvider);
      return GetTransactionHistoryUseCase(repository);
    });

/// Wallet notifier provider
final walletNotifierProvider =
    StateNotifierProvider<WalletNotifier, WalletState>((ref) {
      final repository = ref.watch(walletRepositoryProvider);
      final getWalletUseCase = ref.watch(getWalletUseCaseProvider);
      final addFundsUseCase = ref.watch(addFundsUseCaseProvider);
      final withdrawFundsUseCase = ref.watch(withdrawFundsUseCaseProvider);
      final getTransactionHistoryUseCase = ref.watch(
        getTransactionHistoryUseCaseProvider,
      );

      return WalletNotifier(
        repository: repository,
        getWalletUseCase: getWalletUseCase,
        addFundsUseCase: addFundsUseCase,
        withdrawFundsUseCase: withdrawFundsUseCase,
        getTransactionHistoryUseCase: getTransactionHistoryUseCase,
      );
    });

/// Current wallet state provider
final walletStateProvider = Provider<WalletState>((ref) {
  return ref.watch(walletNotifierProvider);
});

/// Current wallet provider
final currentWalletProvider = Provider((ref) {
  final walletState = ref.watch(walletStateProvider);
  return walletState.wallet;
});

/// Wallet balance provider
final walletBalanceProvider = Provider((ref) {
  final wallet = ref.watch(currentWalletProvider);
  return wallet?.balance;
});

/// Effective balance provider (available for spending)
final effectiveBalanceProvider = Provider((ref) {
  final wallet = ref.watch(currentWalletProvider);
  return wallet?.effectiveBalance;
});

/// Wallet health provider
final walletHealthProvider = Provider((ref) {
  final wallet = ref.watch(currentWalletProvider);
  return wallet?.health;
});

/// Recent transactions provider
final recentTransactionsProvider = Provider((ref) {
  final walletState = ref.watch(walletStateProvider);
  return walletState.recentTransactions;
});

/// Pending transactions provider
final pendingTransactionsProvider = Provider((ref) {
  final walletState = ref.watch(walletStateProvider);
  return walletState.pendingTransactions;
});

/// Payment methods provider
final paymentMethodsProvider = Provider((ref) {
  final walletState = ref.watch(walletStateProvider);
  return walletState.paymentMethods;
});

/// Default payment method provider
final defaultPaymentMethodProvider = Provider((ref) {
  final walletState = ref.watch(walletStateProvider);
  return walletState.defaultPaymentMethod;
});

/// Active payment methods provider
final activePaymentMethodsProvider = Provider((ref) {
  final walletState = ref.watch(walletStateProvider);
  return walletState.activePaymentMethods;
});

/// Wallet loading state provider
final isWalletLoadingProvider = Provider<bool>((ref) {
  final walletState = ref.watch(walletStateProvider);
  return walletState.isLoading;
});

/// Wallet error provider
final walletErrorProvider = Provider((ref) {
  final walletState = ref.watch(walletStateProvider);
  return walletState.error;
});

/// Has wallet error provider
final hasWalletErrorProvider = Provider<bool>((ref) {
  final walletState = ref.watch(walletStateProvider);
  return walletState.hasError;
});

/// Can withdraw funds provider
final canWithdrawFundsProvider = Provider<bool>((ref) {
  final walletState = ref.watch(walletStateProvider);
  return walletState.canWithdraw;
});

/// Can receive funds provider
final canReceiveFundsProvider = Provider<bool>((ref) {
  final walletState = ref.watch(walletStateProvider);
  return walletState.canReceiveFunds;
});

/// Wallet needs attention provider
final walletNeedsAttentionProvider = Provider<bool>((ref) {
  final walletState = ref.watch(walletStateProvider);
  return walletState.needsAttention;
});

/// Total pending amount provider
final totalPendingAmountProvider = Provider((ref) {
  final walletState = ref.watch(walletStateProvider);
  return walletState.totalPendingAmount;
});

/// Recent earnings provider (last 30 days)
final recentEarningsProvider = Provider((ref) {
  final walletState = ref.watch(walletStateProvider);
  return walletState.recentEarnings;
});

/// Recent spending provider (last 30 days)
final recentSpendingProvider = Provider((ref) {
  final walletState = ref.watch(walletStateProvider);
  return walletState.recentSpending;
});

/// Has sufficient funds provider
final hasSufficientFundsProvider = Provider.family<bool, double>((ref, amount) {
  final walletState = ref.watch(walletStateProvider);
  final money = Money(amount: amount);
  return walletState.hasSufficientFunds(money);
});

/// Wallet statistics provider
final walletStatisticsProvider = FutureProvider.family((
  ref,
  String userId,
) async {
  final repository = ref.watch(walletRepositoryProvider);
  final result = await repository.getWalletStatistics(userId: userId);

  return result.fold((failure) => throw failure, (statistics) => statistics);
});

/// Transaction history provider with pagination
final transactionHistoryProvider = FutureProvider.family((
  ref,
  TransactionHistoryParams params,
) async {
  final useCase = ref.watch(getTransactionHistoryUseCaseProvider);
  final result = await useCase(
    GetTransactionHistoryParams(
      userId: params.userId,
      limit: params.limit,
      lastTransactionId: params.lastTransactionId,
      types: params.types,
      statuses: params.statuses,
      startDate: params.startDate,
      endDate: params.endDate,
    ),
  );

  return result.fold(
    (failure) => throw failure,
    (transactions) => transactions,
  );
});

/// Parameters for transaction history provider
class TransactionHistoryParams {
  final String userId;
  final int limit;
  final String? lastTransactionId;
  final List<TransactionType>? types;
  final List<TransactionStatus>? statuses;
  final DateTime? startDate;
  final DateTime? endDate;

  const TransactionHistoryParams({
    required this.userId,
    this.limit = 50,
    this.lastTransactionId,
    this.types,
    this.statuses,
    this.startDate,
    this.endDate,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TransactionHistoryParams &&
          runtimeType == other.runtimeType &&
          userId == other.userId &&
          limit == other.limit &&
          lastTransactionId == other.lastTransactionId &&
          _listEquals(types, other.types) &&
          _listEquals(statuses, other.statuses) &&
          startDate == other.startDate &&
          endDate == other.endDate;

  @override
  int get hashCode =>
      userId.hashCode ^
      limit.hashCode ^
      lastTransactionId.hashCode ^
      types.hashCode ^
      statuses.hashCode ^
      startDate.hashCode ^
      endDate.hashCode;

  /// Helper method to compare lists
  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }
}
