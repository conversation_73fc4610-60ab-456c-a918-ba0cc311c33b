// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wallet_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$WalletState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      Wallet wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )
    loaded,
    required TResult Function(
      Failure failure,
      Wallet? wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )
    error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      Wallet wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    loaded,
    TResult? Function(
      Failure failure,
      Wallet? wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      Wallet wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    loaded,
    TResult Function(
      Failure failure,
      Wallet? wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WalletStateCopyWith<$Res> {
  factory $WalletStateCopyWith(
    WalletState value,
    $Res Function(WalletState) then,
  ) = _$WalletStateCopyWithImpl<$Res, WalletState>;
}

/// @nodoc
class _$WalletStateCopyWithImpl<$Res, $Val extends WalletState>
    implements $WalletStateCopyWith<$Res> {
  _$WalletStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WalletState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
    _$InitialImpl value,
    $Res Function(_$InitialImpl) then,
  ) = __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$WalletStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
    _$InitialImpl _value,
    $Res Function(_$InitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of WalletState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'WalletState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      Wallet wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )
    loaded,
    required TResult Function(
      Failure failure,
      Wallet? wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )
    error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      Wallet wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    loaded,
    TResult? Function(
      Failure failure,
      Wallet? wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      Wallet wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    loaded,
    TResult Function(
      Failure failure,
      Wallet? wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements WalletState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
    _$LoadingImpl value,
    $Res Function(_$LoadingImpl) then,
  ) = __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$WalletStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
    _$LoadingImpl _value,
    $Res Function(_$LoadingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of WalletState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'WalletState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      Wallet wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )
    loaded,
    required TResult Function(
      Failure failure,
      Wallet? wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )
    error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      Wallet wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    loaded,
    TResult? Function(
      Failure failure,
      Wallet? wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      Wallet wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    loaded,
    TResult Function(
      Failure failure,
      Wallet? wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements WalletState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$LoadedImplCopyWith<$Res> {
  factory _$$LoadedImplCopyWith(
    _$LoadedImpl value,
    $Res Function(_$LoadedImpl) then,
  ) = __$$LoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({
    Wallet wallet,
    List<Transaction> recentTransactions,
    List<PaymentMethod> paymentMethods,
  });

  $WalletCopyWith<$Res> get wallet;
}

/// @nodoc
class __$$LoadedImplCopyWithImpl<$Res>
    extends _$WalletStateCopyWithImpl<$Res, _$LoadedImpl>
    implements _$$LoadedImplCopyWith<$Res> {
  __$$LoadedImplCopyWithImpl(
    _$LoadedImpl _value,
    $Res Function(_$LoadedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of WalletState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? wallet = null,
    Object? recentTransactions = null,
    Object? paymentMethods = null,
  }) {
    return _then(
      _$LoadedImpl(
        wallet:
            null == wallet
                ? _value.wallet
                : wallet // ignore: cast_nullable_to_non_nullable
                    as Wallet,
        recentTransactions:
            null == recentTransactions
                ? _value._recentTransactions
                : recentTransactions // ignore: cast_nullable_to_non_nullable
                    as List<Transaction>,
        paymentMethods:
            null == paymentMethods
                ? _value._paymentMethods
                : paymentMethods // ignore: cast_nullable_to_non_nullable
                    as List<PaymentMethod>,
      ),
    );
  }

  /// Create a copy of WalletState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WalletCopyWith<$Res> get wallet {
    return $WalletCopyWith<$Res>(_value.wallet, (value) {
      return _then(_value.copyWith(wallet: value));
    });
  }
}

/// @nodoc

class _$LoadedImpl implements _Loaded {
  const _$LoadedImpl({
    required this.wallet,
    final List<Transaction> recentTransactions = const [],
    final List<PaymentMethod> paymentMethods = const [],
  }) : _recentTransactions = recentTransactions,
       _paymentMethods = paymentMethods;

  @override
  final Wallet wallet;
  final List<Transaction> _recentTransactions;
  @override
  @JsonKey()
  List<Transaction> get recentTransactions {
    if (_recentTransactions is EqualUnmodifiableListView)
      return _recentTransactions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recentTransactions);
  }

  final List<PaymentMethod> _paymentMethods;
  @override
  @JsonKey()
  List<PaymentMethod> get paymentMethods {
    if (_paymentMethods is EqualUnmodifiableListView) return _paymentMethods;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_paymentMethods);
  }

  @override
  String toString() {
    return 'WalletState.loaded(wallet: $wallet, recentTransactions: $recentTransactions, paymentMethods: $paymentMethods)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedImpl &&
            (identical(other.wallet, wallet) || other.wallet == wallet) &&
            const DeepCollectionEquality().equals(
              other._recentTransactions,
              _recentTransactions,
            ) &&
            const DeepCollectionEquality().equals(
              other._paymentMethods,
              _paymentMethods,
            ));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    wallet,
    const DeepCollectionEquality().hash(_recentTransactions),
    const DeepCollectionEquality().hash(_paymentMethods),
  );

  /// Create a copy of WalletState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      __$$LoadedImplCopyWithImpl<_$LoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      Wallet wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )
    loaded,
    required TResult Function(
      Failure failure,
      Wallet? wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )
    error,
  }) {
    return loaded(wallet, recentTransactions, paymentMethods);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      Wallet wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    loaded,
    TResult? Function(
      Failure failure,
      Wallet? wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    error,
  }) {
    return loaded?.call(wallet, recentTransactions, paymentMethods);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      Wallet wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    loaded,
    TResult Function(
      Failure failure,
      Wallet? wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(wallet, recentTransactions, paymentMethods);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _Loaded implements WalletState {
  const factory _Loaded({
    required final Wallet wallet,
    final List<Transaction> recentTransactions,
    final List<PaymentMethod> paymentMethods,
  }) = _$LoadedImpl;

  Wallet get wallet;
  List<Transaction> get recentTransactions;
  List<PaymentMethod> get paymentMethods;

  /// Create a copy of WalletState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
    _$ErrorImpl value,
    $Res Function(_$ErrorImpl) then,
  ) = __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({
    Failure failure,
    Wallet? wallet,
    List<Transaction> recentTransactions,
    List<PaymentMethod> paymentMethods,
  });

  $FailureCopyWith<$Res> get failure;
  $WalletCopyWith<$Res>? get wallet;
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$WalletStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
    _$ErrorImpl _value,
    $Res Function(_$ErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of WalletState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
    Object? wallet = freezed,
    Object? recentTransactions = null,
    Object? paymentMethods = null,
  }) {
    return _then(
      _$ErrorImpl(
        failure:
            null == failure
                ? _value.failure
                : failure // ignore: cast_nullable_to_non_nullable
                    as Failure,
        wallet:
            freezed == wallet
                ? _value.wallet
                : wallet // ignore: cast_nullable_to_non_nullable
                    as Wallet?,
        recentTransactions:
            null == recentTransactions
                ? _value._recentTransactions
                : recentTransactions // ignore: cast_nullable_to_non_nullable
                    as List<Transaction>,
        paymentMethods:
            null == paymentMethods
                ? _value._paymentMethods
                : paymentMethods // ignore: cast_nullable_to_non_nullable
                    as List<PaymentMethod>,
      ),
    );
  }

  /// Create a copy of WalletState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FailureCopyWith<$Res> get failure {
    return $FailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }

  /// Create a copy of WalletState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WalletCopyWith<$Res>? get wallet {
    if (_value.wallet == null) {
      return null;
    }

    return $WalletCopyWith<$Res>(_value.wallet!, (value) {
      return _then(_value.copyWith(wallet: value));
    });
  }
}

/// @nodoc

class _$ErrorImpl implements _Error {
  const _$ErrorImpl({
    required this.failure,
    this.wallet,
    final List<Transaction> recentTransactions = const [],
    final List<PaymentMethod> paymentMethods = const [],
  }) : _recentTransactions = recentTransactions,
       _paymentMethods = paymentMethods;

  @override
  final Failure failure;
  @override
  final Wallet? wallet;
  final List<Transaction> _recentTransactions;
  @override
  @JsonKey()
  List<Transaction> get recentTransactions {
    if (_recentTransactions is EqualUnmodifiableListView)
      return _recentTransactions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recentTransactions);
  }

  final List<PaymentMethod> _paymentMethods;
  @override
  @JsonKey()
  List<PaymentMethod> get paymentMethods {
    if (_paymentMethods is EqualUnmodifiableListView) return _paymentMethods;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_paymentMethods);
  }

  @override
  String toString() {
    return 'WalletState.error(failure: $failure, wallet: $wallet, recentTransactions: $recentTransactions, paymentMethods: $paymentMethods)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.failure, failure) || other.failure == failure) &&
            (identical(other.wallet, wallet) || other.wallet == wallet) &&
            const DeepCollectionEquality().equals(
              other._recentTransactions,
              _recentTransactions,
            ) &&
            const DeepCollectionEquality().equals(
              other._paymentMethods,
              _paymentMethods,
            ));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    failure,
    wallet,
    const DeepCollectionEquality().hash(_recentTransactions),
    const DeepCollectionEquality().hash(_paymentMethods),
  );

  /// Create a copy of WalletState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      Wallet wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )
    loaded,
    required TResult Function(
      Failure failure,
      Wallet? wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )
    error,
  }) {
    return error(failure, wallet, recentTransactions, paymentMethods);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      Wallet wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    loaded,
    TResult? Function(
      Failure failure,
      Wallet? wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    error,
  }) {
    return error?.call(failure, wallet, recentTransactions, paymentMethods);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      Wallet wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    loaded,
    TResult Function(
      Failure failure,
      Wallet? wallet,
      List<Transaction> recentTransactions,
      List<PaymentMethod> paymentMethods,
    )?
    error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(failure, wallet, recentTransactions, paymentMethods);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements WalletState {
  const factory _Error({
    required final Failure failure,
    final Wallet? wallet,
    final List<Transaction> recentTransactions,
    final List<PaymentMethod> paymentMethods,
  }) = _$ErrorImpl;

  Failure get failure;
  Wallet? get wallet;
  List<Transaction> get recentTransactions;
  List<PaymentMethod> get paymentMethods;

  /// Create a copy of WalletState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$TransactionState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function(String operationType, Money? amount) processing,
    required TResult Function(Transaction transaction, String message) success,
    required TResult Function(Failure failure, String operationType) failure,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function(String operationType, Money? amount)? processing,
    TResult? Function(Transaction transaction, String message)? success,
    TResult? Function(Failure failure, String operationType)? failure,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function(String operationType, Money? amount)? processing,
    TResult Function(Transaction transaction, String message)? success,
    TResult Function(Failure failure, String operationType)? failure,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TransactionIdle value) idle,
    required TResult Function(_TransactionProcessing value) processing,
    required TResult Function(_TransactionSuccess value) success,
    required TResult Function(_TransactionFailure value) failure,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TransactionIdle value)? idle,
    TResult? Function(_TransactionProcessing value)? processing,
    TResult? Function(_TransactionSuccess value)? success,
    TResult? Function(_TransactionFailure value)? failure,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TransactionIdle value)? idle,
    TResult Function(_TransactionProcessing value)? processing,
    TResult Function(_TransactionSuccess value)? success,
    TResult Function(_TransactionFailure value)? failure,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransactionStateCopyWith<$Res> {
  factory $TransactionStateCopyWith(
    TransactionState value,
    $Res Function(TransactionState) then,
  ) = _$TransactionStateCopyWithImpl<$Res, TransactionState>;
}

/// @nodoc
class _$TransactionStateCopyWithImpl<$Res, $Val extends TransactionState>
    implements $TransactionStateCopyWith<$Res> {
  _$TransactionStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TransactionState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$TransactionIdleImplCopyWith<$Res> {
  factory _$$TransactionIdleImplCopyWith(
    _$TransactionIdleImpl value,
    $Res Function(_$TransactionIdleImpl) then,
  ) = __$$TransactionIdleImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$TransactionIdleImplCopyWithImpl<$Res>
    extends _$TransactionStateCopyWithImpl<$Res, _$TransactionIdleImpl>
    implements _$$TransactionIdleImplCopyWith<$Res> {
  __$$TransactionIdleImplCopyWithImpl(
    _$TransactionIdleImpl _value,
    $Res Function(_$TransactionIdleImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TransactionState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$TransactionIdleImpl implements _TransactionIdle {
  const _$TransactionIdleImpl();

  @override
  String toString() {
    return 'TransactionState.idle()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$TransactionIdleImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function(String operationType, Money? amount) processing,
    required TResult Function(Transaction transaction, String message) success,
    required TResult Function(Failure failure, String operationType) failure,
  }) {
    return idle();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function(String operationType, Money? amount)? processing,
    TResult? Function(Transaction transaction, String message)? success,
    TResult? Function(Failure failure, String operationType)? failure,
  }) {
    return idle?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function(String operationType, Money? amount)? processing,
    TResult Function(Transaction transaction, String message)? success,
    TResult Function(Failure failure, String operationType)? failure,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TransactionIdle value) idle,
    required TResult Function(_TransactionProcessing value) processing,
    required TResult Function(_TransactionSuccess value) success,
    required TResult Function(_TransactionFailure value) failure,
  }) {
    return idle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TransactionIdle value)? idle,
    TResult? Function(_TransactionProcessing value)? processing,
    TResult? Function(_TransactionSuccess value)? success,
    TResult? Function(_TransactionFailure value)? failure,
  }) {
    return idle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TransactionIdle value)? idle,
    TResult Function(_TransactionProcessing value)? processing,
    TResult Function(_TransactionSuccess value)? success,
    TResult Function(_TransactionFailure value)? failure,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(this);
    }
    return orElse();
  }
}

abstract class _TransactionIdle implements TransactionState {
  const factory _TransactionIdle() = _$TransactionIdleImpl;
}

/// @nodoc
abstract class _$$TransactionProcessingImplCopyWith<$Res> {
  factory _$$TransactionProcessingImplCopyWith(
    _$TransactionProcessingImpl value,
    $Res Function(_$TransactionProcessingImpl) then,
  ) = __$$TransactionProcessingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String operationType, Money? amount});

  $MoneyCopyWith<$Res>? get amount;
}

/// @nodoc
class __$$TransactionProcessingImplCopyWithImpl<$Res>
    extends _$TransactionStateCopyWithImpl<$Res, _$TransactionProcessingImpl>
    implements _$$TransactionProcessingImplCopyWith<$Res> {
  __$$TransactionProcessingImplCopyWithImpl(
    _$TransactionProcessingImpl _value,
    $Res Function(_$TransactionProcessingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TransactionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? operationType = null, Object? amount = freezed}) {
    return _then(
      _$TransactionProcessingImpl(
        operationType:
            null == operationType
                ? _value.operationType
                : operationType // ignore: cast_nullable_to_non_nullable
                    as String,
        amount:
            freezed == amount
                ? _value.amount
                : amount // ignore: cast_nullable_to_non_nullable
                    as Money?,
      ),
    );
  }

  /// Create a copy of TransactionState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MoneyCopyWith<$Res>? get amount {
    if (_value.amount == null) {
      return null;
    }

    return $MoneyCopyWith<$Res>(_value.amount!, (value) {
      return _then(_value.copyWith(amount: value));
    });
  }
}

/// @nodoc

class _$TransactionProcessingImpl implements _TransactionProcessing {
  const _$TransactionProcessingImpl({required this.operationType, this.amount});

  @override
  final String operationType;
  @override
  final Money? amount;

  @override
  String toString() {
    return 'TransactionState.processing(operationType: $operationType, amount: $amount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransactionProcessingImpl &&
            (identical(other.operationType, operationType) ||
                other.operationType == operationType) &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, operationType, amount);

  /// Create a copy of TransactionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TransactionProcessingImplCopyWith<_$TransactionProcessingImpl>
  get copyWith =>
      __$$TransactionProcessingImplCopyWithImpl<_$TransactionProcessingImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function(String operationType, Money? amount) processing,
    required TResult Function(Transaction transaction, String message) success,
    required TResult Function(Failure failure, String operationType) failure,
  }) {
    return processing(operationType, amount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function(String operationType, Money? amount)? processing,
    TResult? Function(Transaction transaction, String message)? success,
    TResult? Function(Failure failure, String operationType)? failure,
  }) {
    return processing?.call(operationType, amount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function(String operationType, Money? amount)? processing,
    TResult Function(Transaction transaction, String message)? success,
    TResult Function(Failure failure, String operationType)? failure,
    required TResult orElse(),
  }) {
    if (processing != null) {
      return processing(operationType, amount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TransactionIdle value) idle,
    required TResult Function(_TransactionProcessing value) processing,
    required TResult Function(_TransactionSuccess value) success,
    required TResult Function(_TransactionFailure value) failure,
  }) {
    return processing(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TransactionIdle value)? idle,
    TResult? Function(_TransactionProcessing value)? processing,
    TResult? Function(_TransactionSuccess value)? success,
    TResult? Function(_TransactionFailure value)? failure,
  }) {
    return processing?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TransactionIdle value)? idle,
    TResult Function(_TransactionProcessing value)? processing,
    TResult Function(_TransactionSuccess value)? success,
    TResult Function(_TransactionFailure value)? failure,
    required TResult orElse(),
  }) {
    if (processing != null) {
      return processing(this);
    }
    return orElse();
  }
}

abstract class _TransactionProcessing implements TransactionState {
  const factory _TransactionProcessing({
    required final String operationType,
    final Money? amount,
  }) = _$TransactionProcessingImpl;

  String get operationType;
  Money? get amount;

  /// Create a copy of TransactionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TransactionProcessingImplCopyWith<_$TransactionProcessingImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TransactionSuccessImplCopyWith<$Res> {
  factory _$$TransactionSuccessImplCopyWith(
    _$TransactionSuccessImpl value,
    $Res Function(_$TransactionSuccessImpl) then,
  ) = __$$TransactionSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Transaction transaction, String message});

  $TransactionCopyWith<$Res> get transaction;
}

/// @nodoc
class __$$TransactionSuccessImplCopyWithImpl<$Res>
    extends _$TransactionStateCopyWithImpl<$Res, _$TransactionSuccessImpl>
    implements _$$TransactionSuccessImplCopyWith<$Res> {
  __$$TransactionSuccessImplCopyWithImpl(
    _$TransactionSuccessImpl _value,
    $Res Function(_$TransactionSuccessImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TransactionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? transaction = null, Object? message = null}) {
    return _then(
      _$TransactionSuccessImpl(
        transaction:
            null == transaction
                ? _value.transaction
                : transaction // ignore: cast_nullable_to_non_nullable
                    as Transaction,
        message:
            null == message
                ? _value.message
                : message // ignore: cast_nullable_to_non_nullable
                    as String,
      ),
    );
  }

  /// Create a copy of TransactionState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TransactionCopyWith<$Res> get transaction {
    return $TransactionCopyWith<$Res>(_value.transaction, (value) {
      return _then(_value.copyWith(transaction: value));
    });
  }
}

/// @nodoc

class _$TransactionSuccessImpl implements _TransactionSuccess {
  const _$TransactionSuccessImpl({
    required this.transaction,
    required this.message,
  });

  @override
  final Transaction transaction;
  @override
  final String message;

  @override
  String toString() {
    return 'TransactionState.success(transaction: $transaction, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransactionSuccessImpl &&
            (identical(other.transaction, transaction) ||
                other.transaction == transaction) &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, transaction, message);

  /// Create a copy of TransactionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TransactionSuccessImplCopyWith<_$TransactionSuccessImpl> get copyWith =>
      __$$TransactionSuccessImplCopyWithImpl<_$TransactionSuccessImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function(String operationType, Money? amount) processing,
    required TResult Function(Transaction transaction, String message) success,
    required TResult Function(Failure failure, String operationType) failure,
  }) {
    return success(transaction, message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function(String operationType, Money? amount)? processing,
    TResult? Function(Transaction transaction, String message)? success,
    TResult? Function(Failure failure, String operationType)? failure,
  }) {
    return success?.call(transaction, message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function(String operationType, Money? amount)? processing,
    TResult Function(Transaction transaction, String message)? success,
    TResult Function(Failure failure, String operationType)? failure,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(transaction, message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TransactionIdle value) idle,
    required TResult Function(_TransactionProcessing value) processing,
    required TResult Function(_TransactionSuccess value) success,
    required TResult Function(_TransactionFailure value) failure,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TransactionIdle value)? idle,
    TResult? Function(_TransactionProcessing value)? processing,
    TResult? Function(_TransactionSuccess value)? success,
    TResult? Function(_TransactionFailure value)? failure,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TransactionIdle value)? idle,
    TResult Function(_TransactionProcessing value)? processing,
    TResult Function(_TransactionSuccess value)? success,
    TResult Function(_TransactionFailure value)? failure,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class _TransactionSuccess implements TransactionState {
  const factory _TransactionSuccess({
    required final Transaction transaction,
    required final String message,
  }) = _$TransactionSuccessImpl;

  Transaction get transaction;
  String get message;

  /// Create a copy of TransactionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TransactionSuccessImplCopyWith<_$TransactionSuccessImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TransactionFailureImplCopyWith<$Res> {
  factory _$$TransactionFailureImplCopyWith(
    _$TransactionFailureImpl value,
    $Res Function(_$TransactionFailureImpl) then,
  ) = __$$TransactionFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Failure failure, String operationType});

  $FailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$TransactionFailureImplCopyWithImpl<$Res>
    extends _$TransactionStateCopyWithImpl<$Res, _$TransactionFailureImpl>
    implements _$$TransactionFailureImplCopyWith<$Res> {
  __$$TransactionFailureImplCopyWithImpl(
    _$TransactionFailureImpl _value,
    $Res Function(_$TransactionFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TransactionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? failure = null, Object? operationType = null}) {
    return _then(
      _$TransactionFailureImpl(
        failure:
            null == failure
                ? _value.failure
                : failure // ignore: cast_nullable_to_non_nullable
                    as Failure,
        operationType:
            null == operationType
                ? _value.operationType
                : operationType // ignore: cast_nullable_to_non_nullable
                    as String,
      ),
    );
  }

  /// Create a copy of TransactionState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FailureCopyWith<$Res> get failure {
    return $FailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$TransactionFailureImpl implements _TransactionFailure {
  const _$TransactionFailureImpl({
    required this.failure,
    required this.operationType,
  });

  @override
  final Failure failure;
  @override
  final String operationType;

  @override
  String toString() {
    return 'TransactionState.failure(failure: $failure, operationType: $operationType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransactionFailureImpl &&
            (identical(other.failure, failure) || other.failure == failure) &&
            (identical(other.operationType, operationType) ||
                other.operationType == operationType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure, operationType);

  /// Create a copy of TransactionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TransactionFailureImplCopyWith<_$TransactionFailureImpl> get copyWith =>
      __$$TransactionFailureImplCopyWithImpl<_$TransactionFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function(String operationType, Money? amount) processing,
    required TResult Function(Transaction transaction, String message) success,
    required TResult Function(Failure failure, String operationType) failure,
  }) {
    return failure(this.failure, operationType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function(String operationType, Money? amount)? processing,
    TResult? Function(Transaction transaction, String message)? success,
    TResult? Function(Failure failure, String operationType)? failure,
  }) {
    return failure?.call(this.failure, operationType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function(String operationType, Money? amount)? processing,
    TResult Function(Transaction transaction, String message)? success,
    TResult Function(Failure failure, String operationType)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this.failure, operationType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TransactionIdle value) idle,
    required TResult Function(_TransactionProcessing value) processing,
    required TResult Function(_TransactionSuccess value) success,
    required TResult Function(_TransactionFailure value) failure,
  }) {
    return failure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TransactionIdle value)? idle,
    TResult? Function(_TransactionProcessing value)? processing,
    TResult? Function(_TransactionSuccess value)? success,
    TResult? Function(_TransactionFailure value)? failure,
  }) {
    return failure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TransactionIdle value)? idle,
    TResult Function(_TransactionProcessing value)? processing,
    TResult Function(_TransactionSuccess value)? success,
    TResult Function(_TransactionFailure value)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this);
    }
    return orElse();
  }
}

abstract class _TransactionFailure implements TransactionState {
  const factory _TransactionFailure({
    required final Failure failure,
    required final String operationType,
  }) = _$TransactionFailureImpl;

  Failure get failure;
  String get operationType;

  /// Create a copy of TransactionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TransactionFailureImplCopyWith<_$TransactionFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$PaymentMethodState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() loading,
    required TResult Function(
      List<PaymentMethod> paymentMethods,
      PaymentMethod? defaultPaymentMethod,
    )
    loaded,
    required TResult Function(
      Failure failure,
      List<PaymentMethod> paymentMethods,
    )
    error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? loading,
    TResult? Function(
      List<PaymentMethod> paymentMethods,
      PaymentMethod? defaultPaymentMethod,
    )?
    loaded,
    TResult? Function(Failure failure, List<PaymentMethod> paymentMethods)?
    error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? loading,
    TResult Function(
      List<PaymentMethod> paymentMethods,
      PaymentMethod? defaultPaymentMethod,
    )?
    loaded,
    TResult Function(Failure failure, List<PaymentMethod> paymentMethods)?
    error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PaymentMethodIdle value) idle,
    required TResult Function(_PaymentMethodLoading value) loading,
    required TResult Function(_PaymentMethodLoaded value) loaded,
    required TResult Function(_PaymentMethodError value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PaymentMethodIdle value)? idle,
    TResult? Function(_PaymentMethodLoading value)? loading,
    TResult? Function(_PaymentMethodLoaded value)? loaded,
    TResult? Function(_PaymentMethodError value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PaymentMethodIdle value)? idle,
    TResult Function(_PaymentMethodLoading value)? loading,
    TResult Function(_PaymentMethodLoaded value)? loaded,
    TResult Function(_PaymentMethodError value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentMethodStateCopyWith<$Res> {
  factory $PaymentMethodStateCopyWith(
    PaymentMethodState value,
    $Res Function(PaymentMethodState) then,
  ) = _$PaymentMethodStateCopyWithImpl<$Res, PaymentMethodState>;
}

/// @nodoc
class _$PaymentMethodStateCopyWithImpl<$Res, $Val extends PaymentMethodState>
    implements $PaymentMethodStateCopyWith<$Res> {
  _$PaymentMethodStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentMethodState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$PaymentMethodIdleImplCopyWith<$Res> {
  factory _$$PaymentMethodIdleImplCopyWith(
    _$PaymentMethodIdleImpl value,
    $Res Function(_$PaymentMethodIdleImpl) then,
  ) = __$$PaymentMethodIdleImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PaymentMethodIdleImplCopyWithImpl<$Res>
    extends _$PaymentMethodStateCopyWithImpl<$Res, _$PaymentMethodIdleImpl>
    implements _$$PaymentMethodIdleImplCopyWith<$Res> {
  __$$PaymentMethodIdleImplCopyWithImpl(
    _$PaymentMethodIdleImpl _value,
    $Res Function(_$PaymentMethodIdleImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PaymentMethodState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PaymentMethodIdleImpl implements _PaymentMethodIdle {
  const _$PaymentMethodIdleImpl();

  @override
  String toString() {
    return 'PaymentMethodState.idle()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$PaymentMethodIdleImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() loading,
    required TResult Function(
      List<PaymentMethod> paymentMethods,
      PaymentMethod? defaultPaymentMethod,
    )
    loaded,
    required TResult Function(
      Failure failure,
      List<PaymentMethod> paymentMethods,
    )
    error,
  }) {
    return idle();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? loading,
    TResult? Function(
      List<PaymentMethod> paymentMethods,
      PaymentMethod? defaultPaymentMethod,
    )?
    loaded,
    TResult? Function(Failure failure, List<PaymentMethod> paymentMethods)?
    error,
  }) {
    return idle?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? loading,
    TResult Function(
      List<PaymentMethod> paymentMethods,
      PaymentMethod? defaultPaymentMethod,
    )?
    loaded,
    TResult Function(Failure failure, List<PaymentMethod> paymentMethods)?
    error,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PaymentMethodIdle value) idle,
    required TResult Function(_PaymentMethodLoading value) loading,
    required TResult Function(_PaymentMethodLoaded value) loaded,
    required TResult Function(_PaymentMethodError value) error,
  }) {
    return idle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PaymentMethodIdle value)? idle,
    TResult? Function(_PaymentMethodLoading value)? loading,
    TResult? Function(_PaymentMethodLoaded value)? loaded,
    TResult? Function(_PaymentMethodError value)? error,
  }) {
    return idle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PaymentMethodIdle value)? idle,
    TResult Function(_PaymentMethodLoading value)? loading,
    TResult Function(_PaymentMethodLoaded value)? loaded,
    TResult Function(_PaymentMethodError value)? error,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(this);
    }
    return orElse();
  }
}

abstract class _PaymentMethodIdle implements PaymentMethodState {
  const factory _PaymentMethodIdle() = _$PaymentMethodIdleImpl;
}

/// @nodoc
abstract class _$$PaymentMethodLoadingImplCopyWith<$Res> {
  factory _$$PaymentMethodLoadingImplCopyWith(
    _$PaymentMethodLoadingImpl value,
    $Res Function(_$PaymentMethodLoadingImpl) then,
  ) = __$$PaymentMethodLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PaymentMethodLoadingImplCopyWithImpl<$Res>
    extends _$PaymentMethodStateCopyWithImpl<$Res, _$PaymentMethodLoadingImpl>
    implements _$$PaymentMethodLoadingImplCopyWith<$Res> {
  __$$PaymentMethodLoadingImplCopyWithImpl(
    _$PaymentMethodLoadingImpl _value,
    $Res Function(_$PaymentMethodLoadingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PaymentMethodState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PaymentMethodLoadingImpl implements _PaymentMethodLoading {
  const _$PaymentMethodLoadingImpl();

  @override
  String toString() {
    return 'PaymentMethodState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentMethodLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() loading,
    required TResult Function(
      List<PaymentMethod> paymentMethods,
      PaymentMethod? defaultPaymentMethod,
    )
    loaded,
    required TResult Function(
      Failure failure,
      List<PaymentMethod> paymentMethods,
    )
    error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? loading,
    TResult? Function(
      List<PaymentMethod> paymentMethods,
      PaymentMethod? defaultPaymentMethod,
    )?
    loaded,
    TResult? Function(Failure failure, List<PaymentMethod> paymentMethods)?
    error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? loading,
    TResult Function(
      List<PaymentMethod> paymentMethods,
      PaymentMethod? defaultPaymentMethod,
    )?
    loaded,
    TResult Function(Failure failure, List<PaymentMethod> paymentMethods)?
    error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PaymentMethodIdle value) idle,
    required TResult Function(_PaymentMethodLoading value) loading,
    required TResult Function(_PaymentMethodLoaded value) loaded,
    required TResult Function(_PaymentMethodError value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PaymentMethodIdle value)? idle,
    TResult? Function(_PaymentMethodLoading value)? loading,
    TResult? Function(_PaymentMethodLoaded value)? loaded,
    TResult? Function(_PaymentMethodError value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PaymentMethodIdle value)? idle,
    TResult Function(_PaymentMethodLoading value)? loading,
    TResult Function(_PaymentMethodLoaded value)? loaded,
    TResult Function(_PaymentMethodError value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _PaymentMethodLoading implements PaymentMethodState {
  const factory _PaymentMethodLoading() = _$PaymentMethodLoadingImpl;
}

/// @nodoc
abstract class _$$PaymentMethodLoadedImplCopyWith<$Res> {
  factory _$$PaymentMethodLoadedImplCopyWith(
    _$PaymentMethodLoadedImpl value,
    $Res Function(_$PaymentMethodLoadedImpl) then,
  ) = __$$PaymentMethodLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({
    List<PaymentMethod> paymentMethods,
    PaymentMethod? defaultPaymentMethod,
  });

  $PaymentMethodCopyWith<$Res>? get defaultPaymentMethod;
}

/// @nodoc
class __$$PaymentMethodLoadedImplCopyWithImpl<$Res>
    extends _$PaymentMethodStateCopyWithImpl<$Res, _$PaymentMethodLoadedImpl>
    implements _$$PaymentMethodLoadedImplCopyWith<$Res> {
  __$$PaymentMethodLoadedImplCopyWithImpl(
    _$PaymentMethodLoadedImpl _value,
    $Res Function(_$PaymentMethodLoadedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PaymentMethodState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paymentMethods = null,
    Object? defaultPaymentMethod = freezed,
  }) {
    return _then(
      _$PaymentMethodLoadedImpl(
        paymentMethods:
            null == paymentMethods
                ? _value._paymentMethods
                : paymentMethods // ignore: cast_nullable_to_non_nullable
                    as List<PaymentMethod>,
        defaultPaymentMethod:
            freezed == defaultPaymentMethod
                ? _value.defaultPaymentMethod
                : defaultPaymentMethod // ignore: cast_nullable_to_non_nullable
                    as PaymentMethod?,
      ),
    );
  }

  /// Create a copy of PaymentMethodState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PaymentMethodCopyWith<$Res>? get defaultPaymentMethod {
    if (_value.defaultPaymentMethod == null) {
      return null;
    }

    return $PaymentMethodCopyWith<$Res>(_value.defaultPaymentMethod!, (value) {
      return _then(_value.copyWith(defaultPaymentMethod: value));
    });
  }
}

/// @nodoc

class _$PaymentMethodLoadedImpl implements _PaymentMethodLoaded {
  const _$PaymentMethodLoadedImpl({
    required final List<PaymentMethod> paymentMethods,
    this.defaultPaymentMethod,
  }) : _paymentMethods = paymentMethods;

  final List<PaymentMethod> _paymentMethods;
  @override
  List<PaymentMethod> get paymentMethods {
    if (_paymentMethods is EqualUnmodifiableListView) return _paymentMethods;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_paymentMethods);
  }

  @override
  final PaymentMethod? defaultPaymentMethod;

  @override
  String toString() {
    return 'PaymentMethodState.loaded(paymentMethods: $paymentMethods, defaultPaymentMethod: $defaultPaymentMethod)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentMethodLoadedImpl &&
            const DeepCollectionEquality().equals(
              other._paymentMethods,
              _paymentMethods,
            ) &&
            (identical(other.defaultPaymentMethod, defaultPaymentMethod) ||
                other.defaultPaymentMethod == defaultPaymentMethod));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_paymentMethods),
    defaultPaymentMethod,
  );

  /// Create a copy of PaymentMethodState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentMethodLoadedImplCopyWith<_$PaymentMethodLoadedImpl> get copyWith =>
      __$$PaymentMethodLoadedImplCopyWithImpl<_$PaymentMethodLoadedImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() loading,
    required TResult Function(
      List<PaymentMethod> paymentMethods,
      PaymentMethod? defaultPaymentMethod,
    )
    loaded,
    required TResult Function(
      Failure failure,
      List<PaymentMethod> paymentMethods,
    )
    error,
  }) {
    return loaded(paymentMethods, defaultPaymentMethod);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? loading,
    TResult? Function(
      List<PaymentMethod> paymentMethods,
      PaymentMethod? defaultPaymentMethod,
    )?
    loaded,
    TResult? Function(Failure failure, List<PaymentMethod> paymentMethods)?
    error,
  }) {
    return loaded?.call(paymentMethods, defaultPaymentMethod);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? loading,
    TResult Function(
      List<PaymentMethod> paymentMethods,
      PaymentMethod? defaultPaymentMethod,
    )?
    loaded,
    TResult Function(Failure failure, List<PaymentMethod> paymentMethods)?
    error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(paymentMethods, defaultPaymentMethod);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PaymentMethodIdle value) idle,
    required TResult Function(_PaymentMethodLoading value) loading,
    required TResult Function(_PaymentMethodLoaded value) loaded,
    required TResult Function(_PaymentMethodError value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PaymentMethodIdle value)? idle,
    TResult? Function(_PaymentMethodLoading value)? loading,
    TResult? Function(_PaymentMethodLoaded value)? loaded,
    TResult? Function(_PaymentMethodError value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PaymentMethodIdle value)? idle,
    TResult Function(_PaymentMethodLoading value)? loading,
    TResult Function(_PaymentMethodLoaded value)? loaded,
    TResult Function(_PaymentMethodError value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _PaymentMethodLoaded implements PaymentMethodState {
  const factory _PaymentMethodLoaded({
    required final List<PaymentMethod> paymentMethods,
    final PaymentMethod? defaultPaymentMethod,
  }) = _$PaymentMethodLoadedImpl;

  List<PaymentMethod> get paymentMethods;
  PaymentMethod? get defaultPaymentMethod;

  /// Create a copy of PaymentMethodState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentMethodLoadedImplCopyWith<_$PaymentMethodLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PaymentMethodErrorImplCopyWith<$Res> {
  factory _$$PaymentMethodErrorImplCopyWith(
    _$PaymentMethodErrorImpl value,
    $Res Function(_$PaymentMethodErrorImpl) then,
  ) = __$$PaymentMethodErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Failure failure, List<PaymentMethod> paymentMethods});

  $FailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$PaymentMethodErrorImplCopyWithImpl<$Res>
    extends _$PaymentMethodStateCopyWithImpl<$Res, _$PaymentMethodErrorImpl>
    implements _$$PaymentMethodErrorImplCopyWith<$Res> {
  __$$PaymentMethodErrorImplCopyWithImpl(
    _$PaymentMethodErrorImpl _value,
    $Res Function(_$PaymentMethodErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PaymentMethodState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? failure = null, Object? paymentMethods = null}) {
    return _then(
      _$PaymentMethodErrorImpl(
        failure:
            null == failure
                ? _value.failure
                : failure // ignore: cast_nullable_to_non_nullable
                    as Failure,
        paymentMethods:
            null == paymentMethods
                ? _value._paymentMethods
                : paymentMethods // ignore: cast_nullable_to_non_nullable
                    as List<PaymentMethod>,
      ),
    );
  }

  /// Create a copy of PaymentMethodState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FailureCopyWith<$Res> get failure {
    return $FailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$PaymentMethodErrorImpl implements _PaymentMethodError {
  const _$PaymentMethodErrorImpl({
    required this.failure,
    final List<PaymentMethod> paymentMethods = const [],
  }) : _paymentMethods = paymentMethods;

  @override
  final Failure failure;
  final List<PaymentMethod> _paymentMethods;
  @override
  @JsonKey()
  List<PaymentMethod> get paymentMethods {
    if (_paymentMethods is EqualUnmodifiableListView) return _paymentMethods;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_paymentMethods);
  }

  @override
  String toString() {
    return 'PaymentMethodState.error(failure: $failure, paymentMethods: $paymentMethods)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentMethodErrorImpl &&
            (identical(other.failure, failure) || other.failure == failure) &&
            const DeepCollectionEquality().equals(
              other._paymentMethods,
              _paymentMethods,
            ));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    failure,
    const DeepCollectionEquality().hash(_paymentMethods),
  );

  /// Create a copy of PaymentMethodState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentMethodErrorImplCopyWith<_$PaymentMethodErrorImpl> get copyWith =>
      __$$PaymentMethodErrorImplCopyWithImpl<_$PaymentMethodErrorImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() loading,
    required TResult Function(
      List<PaymentMethod> paymentMethods,
      PaymentMethod? defaultPaymentMethod,
    )
    loaded,
    required TResult Function(
      Failure failure,
      List<PaymentMethod> paymentMethods,
    )
    error,
  }) {
    return error(failure, paymentMethods);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? loading,
    TResult? Function(
      List<PaymentMethod> paymentMethods,
      PaymentMethod? defaultPaymentMethod,
    )?
    loaded,
    TResult? Function(Failure failure, List<PaymentMethod> paymentMethods)?
    error,
  }) {
    return error?.call(failure, paymentMethods);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? loading,
    TResult Function(
      List<PaymentMethod> paymentMethods,
      PaymentMethod? defaultPaymentMethod,
    )?
    loaded,
    TResult Function(Failure failure, List<PaymentMethod> paymentMethods)?
    error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(failure, paymentMethods);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PaymentMethodIdle value) idle,
    required TResult Function(_PaymentMethodLoading value) loading,
    required TResult Function(_PaymentMethodLoaded value) loaded,
    required TResult Function(_PaymentMethodError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PaymentMethodIdle value)? idle,
    TResult? Function(_PaymentMethodLoading value)? loading,
    TResult? Function(_PaymentMethodLoaded value)? loaded,
    TResult? Function(_PaymentMethodError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PaymentMethodIdle value)? idle,
    TResult Function(_PaymentMethodLoading value)? loading,
    TResult Function(_PaymentMethodLoaded value)? loaded,
    TResult Function(_PaymentMethodError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _PaymentMethodError implements PaymentMethodState {
  const factory _PaymentMethodError({
    required final Failure failure,
    final List<PaymentMethod> paymentMethods,
  }) = _$PaymentMethodErrorImpl;

  Failure get failure;
  List<PaymentMethod> get paymentMethods;

  /// Create a copy of PaymentMethodState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentMethodErrorImplCopyWith<_$PaymentMethodErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
