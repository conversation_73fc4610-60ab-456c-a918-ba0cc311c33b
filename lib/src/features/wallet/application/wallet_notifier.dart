import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/domain/entities.dart';
import '../../../core/errors/failures.dart';
import '../domain/entities/wallet.dart';
import '../domain/entities/transaction.dart';
import '../domain/entities/payment_method.dart';
import '../domain/repositories/wallet_repository.dart';
import '../domain/usecases/add_funds_usecase.dart';
import '../domain/usecases/withdraw_funds_usecase.dart';
import '../domain/usecases/get_wallet_usecase.dart';
import '../domain/usecases/get_transaction_history_usecase.dart';
import 'wallet_state.dart';

/// Notifier for managing wallet state
class WalletNotifier extends StateNotifier<WalletState> {
  final WalletRepository _repository;
  final GetWalletUseCase _getWalletUseCase;
  final AddFundsUseCase _addFundsUseCase;
  final WithdrawFundsUseCase _withdrawFundsUseCase;
  final GetTransactionHistoryUseCase _getTransactionHistoryUseCase;
  
  StreamSubscription<Wallet?>? _walletSubscription;
  String? _currentUserId;

  WalletNotifier({
    required WalletRepository repository,
    required GetWalletUseCase getWalletUseCase,
    required AddFundsUseCase addFundsUseCase,
    required WithdrawFundsUseCase withdrawFundsUseCase,
    required GetTransactionHistoryUseCase getTransactionHistoryUseCase,
  })  : _repository = repository,
        _getWalletUseCase = getWalletUseCase,
        _addFundsUseCase = addFundsUseCase,
        _withdrawFundsUseCase = withdrawFundsUseCase,
        _getTransactionHistoryUseCase = getTransactionHistoryUseCase,
        super(const WalletState.initial());

  /// Initialize wallet for user
  Future<void> initializeWallet(String userId) async {
    if (_currentUserId == userId && state.isLoaded) return;

    _currentUserId = userId;
    state = const WalletState.loading();

    try {
      // Cancel existing subscription
      await _walletSubscription?.cancel();

      // Start watching wallet changes
      _walletSubscription = _getWalletUseCase.watchWallet(userId).listen(
        (wallet) async {
          if (wallet != null) {
            await _loadWalletData(wallet);
          } else {
            // Create wallet if it doesn't exist
            await _createWallet(userId);
          }
        },
        onError: (error) {
          state = WalletState.error(
            failure: error is Failure 
                ? error 
                : Failure.unknown(
                    message: 'Error watching wallet',
                    exception: error,
                  ),
          );
        },
      );
    } catch (e) {
      state = WalletState.error(
        failure: Failure.unknown(
          message: 'Failed to initialize wallet',
          exception: e,
        ),
      );
    }
  }

  /// Load wallet data including transactions and payment methods
  Future<void> _loadWalletData(Wallet wallet) async {
    try {
      // Load recent transactions
      final transactionsResult = await _getTransactionHistoryUseCase(
        GetTransactionHistoryParams(
          userId: wallet.userId,
          limit: 20,
        ),
      );

      final transactions = transactionsResult.fold(
        (failure) => <Transaction>[],
        (transactions) => transactions,
      );

      // Load payment methods
      final paymentMethodsResult = await _repository.getPaymentMethods(
        userId: wallet.userId,
      );

      final paymentMethods = paymentMethodsResult.fold(
        (failure) => <PaymentMethod>[],
        (methods) => methods,
      );

      state = WalletState.loaded(
        wallet: wallet,
        recentTransactions: transactions,
        paymentMethods: paymentMethods,
      );
    } catch (e) {
      state = WalletState.error(
        failure: Failure.unknown(
          message: 'Failed to load wallet data',
          exception: e,
        ),
        wallet: wallet,
      );
    }
  }

  /// Create wallet for user
  Future<void> _createWallet(String userId) async {
    try {
      final result = await _repository.createWallet(userId: userId);
      
      result.fold(
        (failure) => state = WalletState.error(failure: failure),
        (wallet) => _loadWalletData(wallet),
      );
    } catch (e) {
      state = WalletState.error(
        failure: Failure.unknown(
          message: 'Failed to create wallet',
          exception: e,
        ),
      );
    }
  }

  /// Refresh wallet data
  Future<void> refreshWallet() async {
    if (_currentUserId == null) return;

    try {
      final result = await _getWalletUseCase(_currentUserId!);
      
      result.fold(
        (failure) => state = WalletState.error(
          failure: failure,
          wallet: state.wallet,
          recentTransactions: state.recentTransactions,
          paymentMethods: state.paymentMethods,
        ),
        (wallet) {
          if (wallet != null) {
            _loadWalletData(wallet);
          }
        },
      );
    } catch (e) {
      state = WalletState.error(
        failure: Failure.unknown(
          message: 'Failed to refresh wallet',
          exception: e,
        ),
        wallet: state.wallet,
        recentTransactions: state.recentTransactions,
        paymentMethods: state.paymentMethods,
      );
    }
  }

  /// Add funds to wallet
  Future<Transaction?> addFunds({
    required Money amount,
    required String paymentMethodId,
    String? description,
  }) async {
    if (_currentUserId == null) return null;

    try {
      final params = AddFundsParams(
        userId: _currentUserId!,
        amount: amount,
        paymentMethodId: paymentMethodId,
        description: description,
      );

      final result = await _addFundsUseCase(params);

      return result.fold(
        (failure) {
          state = WalletState.error(
            failure: failure,
            wallet: state.wallet,
            recentTransactions: state.recentTransactions,
            paymentMethods: state.paymentMethods,
          );
          return null;
        },
        (transaction) {
          // Wallet will be updated automatically via stream
          return transaction;
        },
      );
    } catch (e) {
      state = WalletState.error(
        failure: Failure.unknown(
          message: 'Failed to add funds',
          exception: e,
        ),
        wallet: state.wallet,
        recentTransactions: state.recentTransactions,
        paymentMethods: state.paymentMethods,
      );
      return null;
    }
  }

  /// Withdraw funds from wallet
  Future<Transaction?> withdrawFunds({
    required Money amount,
    required String paymentMethodId,
    String? description,
  }) async {
    if (_currentUserId == null) return null;

    try {
      final params = WithdrawFundsParams(
        userId: _currentUserId!,
        amount: amount,
        paymentMethodId: paymentMethodId,
        description: description,
      );

      final result = await _withdrawFundsUseCase(params);

      return result.fold(
        (failure) {
          state = WalletState.error(
            failure: failure,
            wallet: state.wallet,
            recentTransactions: state.recentTransactions,
            paymentMethods: state.paymentMethods,
          );
          return null;
        },
        (transaction) {
          // Wallet will be updated automatically via stream
          return transaction;
        },
      );
    } catch (e) {
      state = WalletState.error(
        failure: Failure.unknown(
          message: 'Failed to withdraw funds',
          exception: e,
        ),
        wallet: state.wallet,
        recentTransactions: state.recentTransactions,
        paymentMethods: state.paymentMethods,
      );
      return null;
    }
  }

  /// Load more transactions
  Future<void> loadMoreTransactions() async {
    if (_currentUserId == null || !state.isLoaded) return;

    try {
      final lastTransaction = state.recentTransactions.lastOrNull;
      
      final result = await _getTransactionHistoryUseCase(
        GetTransactionHistoryParams(
          userId: _currentUserId!,
          limit: 20,
          lastTransactionId: lastTransaction?.id,
        ),
      );

      result.fold(
        (failure) {
          // Don't update state for pagination errors, just ignore
        },
        (newTransactions) {
          if (newTransactions.isNotEmpty) {
            final updatedTransactions = [
              ...state.recentTransactions,
              ...newTransactions,
            ];
            
            state = state.maybeWhen(
              loaded: (wallet, _, paymentMethods) => WalletState.loaded(
                wallet: wallet,
                recentTransactions: updatedTransactions,
                paymentMethods: paymentMethods,
              ),
              orElse: () => state,
            );
          }
        },
      );
    } catch (e) {
      // Ignore pagination errors
    }
  }

  /// Clear error state
  void clearError() {
    state.maybeWhen(
      error: (failure, wallet, transactions, paymentMethods) {
        if (wallet != null) {
          state = WalletState.loaded(
            wallet: wallet,
            recentTransactions: transactions,
            paymentMethods: paymentMethods,
          );
        } else {
          state = const WalletState.initial();
        }
      },
      orElse: () {},
    );
  }

  @override
  void dispose() {
    _walletSubscription?.cancel();
    super.dispose();
  }
}

/// Extension to add lastOrNull method if not available
extension ListExtension<T> on List<T> {
  T? get lastOrNull {
    if (isEmpty) return null;
    return last;
  }
}
