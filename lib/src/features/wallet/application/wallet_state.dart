import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../core/errors/failures.dart';
import '../../../core/domain/entities.dart';
import '../domain/entities/wallet.dart';
import '../domain/entities/transaction.dart';
import '../domain/entities/payment_method.dart';

part 'wallet_state.freezed.dart';

/// Wallet state for the application
@freezed
class WalletState with _$WalletState {
  const factory WalletState.initial() = _Initial;
  
  const factory WalletState.loading() = _Loading;
  
  const factory WalletState.loaded({
    required Wallet wallet,
    @Default([]) List<Transaction> recentTransactions,
    @Default([]) List<PaymentMethod> paymentMethods,
  }) = _Loaded;
  
  const factory WalletState.error({
    required Failure failure,
    Wallet? wallet,
    @Default([]) List<Transaction> recentTransactions,
    @Default([]) List<PaymentMethod> paymentMethods,
  }) = _Error;
}

/// Transaction operation state
@freezed
class TransactionState with _$TransactionState {
  const factory TransactionState.idle() = _TransactionIdle;
  
  const factory TransactionState.processing({
    required String operationType,
    Money? amount,
  }) = _TransactionProcessing;
  
  const factory TransactionState.success({
    required Transaction transaction,
    required String message,
  }) = _TransactionSuccess;
  
  const factory TransactionState.failure({
    required Failure failure,
    required String operationType,
  }) = _TransactionFailure;
}

/// Payment method state
@freezed
class PaymentMethodState with _$PaymentMethodState {
  const factory PaymentMethodState.idle() = _PaymentMethodIdle;
  
  const factory PaymentMethodState.loading() = _PaymentMethodLoading;
  
  const factory PaymentMethodState.loaded({
    required List<PaymentMethod> paymentMethods,
    PaymentMethod? defaultPaymentMethod,
  }) = _PaymentMethodLoaded;
  
  const factory PaymentMethodState.error({
    required Failure failure,
    @Default([]) List<PaymentMethod> paymentMethods,
  }) = _PaymentMethodError;
}

/// Extension methods for WalletState
extension WalletStateExtension on WalletState {
  /// Check if wallet is loaded
  bool get isLoaded => maybeWhen(
    loaded: (_, __, ___) => true,
    orElse: () => false,
  );

  /// Check if wallet is loading
  bool get isLoading => maybeWhen(
    loading: () => true,
    orElse: () => false,
  );

  /// Check if there's an error
  bool get hasError => maybeWhen(
    error: (_, __, ___, ____) => true,
    orElse: () => false,
  );

  /// Get the current wallet if available
  Wallet? get wallet => maybeWhen(
    loaded: (wallet, _, __) => wallet,
    error: (_, wallet, __, ___) => wallet,
    orElse: () => null,
  );

  /// Get recent transactions
  List<Transaction> get recentTransactions => maybeWhen(
    loaded: (_, transactions, __) => transactions,
    error: (_, __, transactions, ___) => transactions,
    orElse: () => [],
  );

  /// Get payment methods
  List<PaymentMethod> get paymentMethods => maybeWhen(
    loaded: (_, __, paymentMethods) => paymentMethods,
    error: (_, __, ___, paymentMethods) => paymentMethods,
    orElse: () => [],
  );

  /// Get the current error if any
  Failure? get error => maybeWhen(
    error: (failure, _, __, ___) => failure,
    orElse: () => null,
  );

  /// Get current balance
  Money? get balance => wallet?.balance;

  /// Get effective balance (available for spending)
  Money? get effectiveBalance => wallet?.effectiveBalance;

  /// Check if wallet has sufficient funds for amount
  bool hasSufficientFunds(Money amount) => 
      wallet?.hasSufficientFunds(amount) ?? false;

  /// Check if wallet can withdraw funds
  bool get canWithdraw => wallet?.canWithdraw ?? false;

  /// Check if wallet can receive funds
  bool get canReceiveFunds => wallet?.canReceiveFunds ?? false;

  /// Get wallet health status
  WalletHealth? get walletHealth => wallet?.health;

  /// Check if wallet needs attention
  bool get needsAttention => wallet?.needsAttention ?? false;

  /// Get default payment method
  PaymentMethod? get defaultPaymentMethod => 
      paymentMethods.where((pm) => pm.isDefault).firstOrNull;

  /// Get active payment methods
  List<PaymentMethod> get activePaymentMethods => 
      paymentMethods.where((pm) => pm.isActive).toList();

  /// Get pending transactions
  List<Transaction> get pendingTransactions => 
      recentTransactions.where((t) => t.isPending).toList();

  /// Get completed transactions
  List<Transaction> get completedTransactions => 
      recentTransactions.where((t) => t.isCompleted).toList();

  /// Get failed transactions
  List<Transaction> get failedTransactions => 
      recentTransactions.where((t) => t.isFailed).toList();

  /// Get total pending amount
  Money get totalPendingAmount {
    final pendingAmount = pendingTransactions
        .map((t) => t.effectiveAmount)
        .fold(Money.zero(), (sum, amount) => sum + amount);
    return pendingAmount;
  }

  /// Get recent earnings (last 30 days)
  Money get recentEarnings {
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    final earnings = recentTransactions
        .where((t) => t.isCredit && t.createdAt.isAfter(thirtyDaysAgo))
        .map((t) => t.amount)
        .fold(Money.zero(), (sum, amount) => sum + amount);
    return earnings;
  }

  /// Get recent spending (last 30 days)
  Money get recentSpending {
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    final spending = recentTransactions
        .where((t) => t.isDebit && t.createdAt.isAfter(thirtyDaysAgo))
        .map((t) => t.amount)
        .fold(Money.zero(), (sum, amount) => sum + amount);
    return spending;
  }
}

/// Extension methods for TransactionState
extension TransactionStateExtension on TransactionState {
  /// Check if transaction is processing
  bool get isProcessing => maybeWhen(
    processing: (_, __) => true,
    orElse: () => false,
  );

  /// Check if transaction was successful
  bool get isSuccess => maybeWhen(
    success: (_, __) => true,
    orElse: () => false,
  );

  /// Check if transaction failed
  bool get isFailure => maybeWhen(
    failure: (_, __) => true,
    orElse: () => false,
  );

  /// Get the operation type if processing or failed
  String? get operationType => maybeWhen(
    processing: (type, _) => type,
    failure: (_, type) => type,
    orElse: () => null,
  );

  /// Get the transaction if successful
  Transaction? get transaction => maybeWhen(
    success: (transaction, _) => transaction,
    orElse: () => null,
  );

  /// Get the error if failed
  Failure? get error => maybeWhen(
    failure: (failure, _) => failure,
    orElse: () => null,
  );
}

/// Extension to add firstOrNull method if not available
extension IterableExtension<T> on Iterable<T> {
  T? get firstOrNull {
    final iterator = this.iterator;
    if (iterator.moveNext()) {
      return iterator.current;
    }
    return null;
  }
}
