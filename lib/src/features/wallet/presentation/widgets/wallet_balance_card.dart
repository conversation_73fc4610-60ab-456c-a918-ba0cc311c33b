import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../application/wallet_providers.dart';
import '../../domain/entities/wallet.dart';
import 'add_funds_dialog.dart';
import 'withdraw_funds_dialog.dart';

/// Widget displaying the main wallet balance card
class WalletBalanceCard extends ConsumerWidget {
  const WalletBalanceCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final wallet = ref.watch(currentWalletProvider);
    final balance = ref.watch(walletBalanceProvider);
    final effectiveBalance = ref.watch(effectiveBalanceProvider);
    final walletHealth = ref.watch(walletHealthProvider);
    final needsAttention = ref.watch(walletNeedsAttentionProvider);

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.green.shade600, Colors.green.shade800],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.green.shade200,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with wallet status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Current Balance',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (needsAttention)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade600,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.warning, size: 12, color: Colors.white),
                        const SizedBox(width: 4),
                        Text(
                          'Attention',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),

            // Main balance display
            Text(
              balance != null
                  ? '\$${balance.amount.toStringAsFixed(2)}'
                  : '\$0.00',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 36,
                fontWeight: FontWeight.bold,
              ),
            ),

            // Effective balance if different
            if (effectiveBalance != null &&
                balance != null &&
                effectiveBalance.amount != balance.amount) ...[
              const SizedBox(height: 4),
              Text(
                'Available: \$${effectiveBalance.amount.toStringAsFixed(2)}',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],

            const SizedBox(height: 16),

            // Wallet health indicator
            if (walletHealth != null)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _getHealthColor(walletHealth).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: _getHealthColor(walletHealth).withOpacity(0.5),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getHealthIcon(walletHealth),
                      size: 16,
                      color: _getHealthColor(walletHealth),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      walletHealth.displayName,
                      style: TextStyle(
                        color: _getHealthColor(walletHealth),
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 20),

            // Quick action buttons
            Row(
              children: [
                Expanded(
                  child: _QuickActionButton(
                    icon: Icons.add,
                    label: 'ReUp!',
                    onTap: () => _showAddFundsDialog(context),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _QuickActionButton(
                    icon: Icons.remove,
                    label: 'Withdraw',
                    onTap: () => _showWithdrawDialog(context),
                    enabled: ref.watch(canWithdrawFundsProvider),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getHealthColor(WalletHealth health) {
    switch (health) {
      case WalletHealth.healthy:
        return Colors.green;
      case WalletHealth.moderate:
        return Colors.yellow;
      case WalletHealth.low:
        return Colors.orange;
      case WalletHealth.overdrawn:
        return Colors.red;
      case WalletHealth.inactive:
        return Colors.grey;
    }
  }

  IconData _getHealthIcon(WalletHealth health) {
    switch (health) {
      case WalletHealth.healthy:
        return Icons.check_circle;
      case WalletHealth.moderate:
        return Icons.warning;
      case WalletHealth.low:
        return Icons.error_outline;
      case WalletHealth.overdrawn:
        return Icons.error;
      case WalletHealth.inactive:
        return Icons.block;
    }
  }

  void _showAddFundsDialog(BuildContext context) {
    showDialog(context: context, builder: (context) => const AddFundsDialog());
  }

  void _showWithdrawDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const WithdrawFundsDialog(),
    );
  }
}

/// Quick action button widget
class _QuickActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;
  final bool enabled;

  const _QuickActionButton({
    required this.icon,
    required this.label,
    required this.onTap,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white.withOpacity(enabled ? 0.2 : 0.1),
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: enabled ? onTap : null,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: Colors.white.withOpacity(enabled ? 1.0 : 0.5),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  color: Colors.white.withOpacity(enabled ? 1.0 : 0.5),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
