import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../application/wallet_providers.dart';
import '../../domain/entities/transaction.dart';
import '../screens/wallet_screen_v2.dart';

/// Widget displaying filtered transaction list
class TransactionListView extends ConsumerWidget {
  final TransactionFilter filter;
  final VoidCallback? onLoadMore;

  const TransactionListView({
    super.key,
    required this.filter,
    this.onLoadMore,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final allTransactions = ref.watch(recentTransactionsProvider);
    final filteredTransactions = _filterTransactions(allTransactions);

    if (filteredTransactions.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredTransactions.length + 1, // +1 for load more button
      itemBuilder: (context, index) {
        if (index == filteredTransactions.length) {
          return _buildLoadMoreButton();
        }

        final transaction = filteredTransactions[index];
        return _TransactionTile(transaction: transaction);
      },
    );
  }

  List<Transaction> _filterTransactions(List<Transaction> transactions) {
    switch (filter) {
      case TransactionFilter.all:
        return transactions;
      case TransactionFilter.income:
        return transactions.where((t) => t.isCredit).toList();
      case TransactionFilter.expenses:
        return transactions.where((t) => t.isDebit).toList();
    }
  }

  Widget _buildEmptyState() {
    String message;
    IconData icon;
    
    switch (filter) {
      case TransactionFilter.all:
        message = 'No transactions yet';
        icon = Icons.receipt_long;
        break;
      case TransactionFilter.income:
        message = 'No income transactions';
        icon = Icons.trending_up;
        break;
      case TransactionFilter.expenses:
        message = 'No expense transactions';
        icon = Icons.trending_down;
        break;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your transactions will appear here',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadMoreButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Center(
        child: TextButton.icon(
          onPressed: onLoadMore,
          icon: const Icon(Icons.refresh),
          label: const Text('Load More'),
          style: TextButton.styleFrom(
            foregroundColor: Colors.green.shade600,
          ),
        ),
      ),
    );
  }
}

/// Individual transaction tile widget
class _TransactionTile extends StatelessWidget {
  final Transaction transaction;

  const _TransactionTile({required this.transaction});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: _buildTransactionIcon(),
        title: Text(
          transaction.description,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              transaction.displayTypeName,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              transaction.timeDisplay,
              style: TextStyle(
                color: Colors.grey.shade500,
                fontSize: 11,
              ),
            ),
          ],
        ),
        trailing: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '${transaction.isCredit ? '+' : '-'}\$${transaction.amount.amount.toStringAsFixed(2)}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: _getAmountColor(),
              ),
            ),
            const SizedBox(height: 4),
            _buildStatusChip(),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionIcon() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: _getIconBackgroundColor(),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        _getTransactionIcon(),
        color: _getIconColor(),
        size: 20,
      ),
    );
  }

  Widget _buildStatusChip() {
    if (transaction.isCompleted) {
      return const SizedBox.shrink(); // Don't show chip for completed transactions
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: _getStatusColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getStatusColor().withOpacity(0.3),
          width: 0.5,
        ),
      ),
      child: Text(
        transaction.displayStatusName,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: _getStatusColor(),
        ),
      ),
    );
  }

  IconData _getTransactionIcon() {
    switch (transaction.type) {
      case TransactionType.credit:
        return Icons.add_circle;
      case TransactionType.debit:
        return Icons.remove_circle;
      case TransactionType.withdrawal:
        return Icons.account_balance;
      case TransactionType.postPayment:
        return Icons.post_add;
      case TransactionType.refund:
        return Icons.refresh;
      case TransactionType.fee:
        return Icons.receipt;
    }
  }

  Color _getIconColor() {
    if (transaction.isFailed || transaction.isCancelled) {
      return Colors.red.shade600;
    }
    if (transaction.isPending || transaction.isProcessing) {
      return Colors.orange.shade600;
    }
    return transaction.isCredit ? Colors.green.shade600 : Colors.red.shade600;
  }

  Color _getIconBackgroundColor() {
    return _getIconColor().withOpacity(0.1);
  }

  Color _getAmountColor() {
    if (transaction.isFailed || transaction.isCancelled) {
      return Colors.grey.shade600;
    }
    return transaction.isCredit ? Colors.green.shade600 : Colors.red.shade600;
  }

  Color _getStatusColor() {
    switch (transaction.status) {
      case TransactionStatus.pending:
        return Colors.orange;
      case TransactionStatus.completed:
        return Colors.green;
      case TransactionStatus.failed:
        return Colors.red;
      case TransactionStatus.cancelled:
        return Colors.grey;
      case TransactionStatus.processing:
        return Colors.blue;
    }
  }
}
