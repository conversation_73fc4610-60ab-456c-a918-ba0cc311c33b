import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../application/wallet_providers.dart';

/// Widget displaying wallet statistics cards
class WalletStatsCards extends ConsumerWidget {
  const WalletStatsCards({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final recentEarnings = ref.watch(recentEarningsProvider);
    final recentSpending = ref.watch(recentSpendingProvider);
    final totalPending = ref.watch(totalPendingAmountProvider);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // Earnings Card
          Expanded(
            child: _StatCard(
              icon: Icons.trending_up,
              iconColor: Colors.green,
              title: 'Total Earned',
              subtitle: 'Last 30 days',
              amount: recentEarnings.amount,
              isPositive: true,
            ),
          ),
          const SizedBox(width: 8),
          
          // Spending Card
          Expanded(
            child: _StatCard(
              icon: Icons.trending_down,
              iconColor: Colors.red,
              title: 'Total Spent',
              subtitle: 'Last 30 days',
              amount: recentSpending.amount,
              isPositive: false,
            ),
          ),
          
          // Pending Card (if there are pending transactions)
          if (totalPending.isPositive) ...[
            const SizedBox(width: 8),
            Expanded(
              child: _StatCard(
                icon: Icons.schedule,
                iconColor: Colors.orange,
                title: 'Pending',
                subtitle: 'Processing',
                amount: totalPending.amount,
                isPositive: null, // Neutral color
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Individual stat card widget
class _StatCard extends StatelessWidget {
  final IconData icon;
  final Color iconColor;
  final String title;
  final String subtitle;
  final double amount;
  final bool? isPositive; // null for neutral

  const _StatCard({
    required this.icon,
    required this.iconColor,
    required this.title,
    required this.subtitle,
    required this.amount,
    required this.isPositive,
  });

  @override
  Widget build(BuildContext context) {
    final amountColor = isPositive == null 
        ? Colors.grey.shade700
        : isPositive! 
            ? Colors.green 
            : Colors.red;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icon
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: iconColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 20,
              ),
            ),
            const SizedBox(height: 12),
            
            // Title
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            
            // Amount
            Text(
              '\$${amount.toStringAsFixed(2)}',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: amountColor,
              ),
            ),
            const SizedBox(height: 4),
            
            // Subtitle
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
