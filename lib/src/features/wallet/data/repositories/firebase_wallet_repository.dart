import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/domain/entities.dart';
import '../../domain/entities/wallet.dart';
import '../../domain/entities/transaction.dart' as domain;
import '../../domain/entities/payment_method.dart';
import '../../domain/repositories/wallet_repository.dart';
import '../models/wallet_dto.dart';
import '../models/transaction_dto.dart';
import '../services/payment_service_adapter.dart';

/// Firebase implementation of WalletRepository
class FirebaseWalletRepository implements WalletRepository {
  final FirebaseFirestore _firestore;
  final PaymentServiceAdapter _paymentService;

  FirebaseWalletRepository({
    required FirebaseFirestore firestore,
    required PaymentServiceAdapter paymentService,
  }) : _firestore = firestore,
       _paymentService = paymentService;

  @override
  Stream<Wallet?> watchWallet(String userId) {
    return _firestore
        .collection('wallets')
        .doc(userId)
        .snapshots()
        .map((snapshot) {
          if (!snapshot.exists) return null;

          final walletDto = WalletDto.fromFirestore(
            snapshot.data()!,
            snapshot.id,
          );
          return walletDto.toDomain();
        })
        .handleError((error) {
          throw Failure.unknown(
            message: 'Error watching wallet',
            exception: error,
          );
        });
  }

  @override
  Future<Either<Failure, Wallet?>> getWallet(String userId) async {
    try {
      final doc = await _firestore.collection('wallets').doc(userId).get();

      if (!doc.exists) return const Right(null);

      final walletDto = WalletDto.fromFirestore(doc.data()!, doc.id);
      return Right(walletDto.toDomain());
    } on FirebaseException catch (e) {
      return Left(_mapFirebaseException(e));
    } catch (e) {
      return Left(
        Failure.unknown(message: 'Failed to get wallet', exception: e),
      );
    }
  }

  @override
  Future<Either<Failure, Wallet>> createWallet({
    required String userId,
    String currency = 'USD',
  }) async {
    try {
      final now = DateTime.now();
      final walletDto = WalletDto(
        id: userId,
        userId: userId,
        balance: 0.0,
        currency: currency,
        createdAt: now.millisecondsSinceEpoch,
        updatedAt: now.millisecondsSinceEpoch,
        isActive: true,
      );

      await _firestore
          .collection('wallets')
          .doc(userId)
          .set(walletDto.toFirestore());

      return Right(walletDto.toDomain());
    } on FirebaseException catch (e) {
      return Left(_mapFirebaseException(e));
    } catch (e) {
      return Left(
        Failure.unknown(message: 'Failed to create wallet', exception: e),
      );
    }
  }

  @override
  Future<Either<Failure, Wallet>> updateBalance({
    required String userId,
    required Money newBalance,
  }) async {
    try {
      final walletRef = _firestore.collection('wallets').doc(userId);

      await _firestore.runTransaction((transaction) async {
        final walletDoc = await transaction.get(walletRef);

        if (!walletDoc.exists) {
          throw Exception('Wallet not found');
        }

        final walletDto = WalletDto.fromFirestore(
          walletDoc.data()!,
          walletDoc.id,
        );
        final updatedDto = walletDto.withUpdatedBalance(newBalance.amount);

        transaction.update(walletRef, updatedDto.toFirestore());
      });

      // Get updated wallet
      final updatedWalletResult = await getWallet(userId);
      return updatedWalletResult.fold(
        (failure) => Left(failure),
        (wallet) =>
            wallet != null
                ? Right(wallet)
                : const Left(
                  Failure.notFound(message: 'Wallet not found after update'),
                ),
      );
    } on FirebaseException catch (e) {
      return Left(_mapFirebaseException(e));
    } catch (e) {
      return Left(
        Failure.unknown(message: 'Failed to update balance', exception: e),
      );
    }
  }

  @override
  Future<Either<Failure, domain.Transaction>> addFunds({
    required String userId,
    required Money amount,
    required String paymentMethodId,
    String? description,
  }) async {
    try {
      // Process payment first
      final paymentResult = await _paymentService.processPayment(
        amount: amount,
        paymentMethodId: paymentMethodId,
      );

      if (!paymentResult) {
        return const Left(Failure.server(message: 'Payment processing failed'));
      }

      // Create transaction
      final transactionDto = TransactionDto(
        id: _generateTransactionId(),
        type: 'credit',
        amount: amount.amount,
        currency: amount.currency,
        description: description ?? 'Added funds',
        createdAt: DateTime.now().millisecondsSinceEpoch,
        status: 'completed',
        paymentIntentId: paymentMethodId,
      );

      // Update wallet balance and add transaction in a transaction
      await _firestore.runTransaction((transaction) async {
        final walletRef = _firestore.collection('wallets').doc(userId);
        final walletDoc = await transaction.get(walletRef);

        if (!walletDoc.exists) {
          throw Exception('Wallet not found');
        }

        final walletDto = WalletDto.fromFirestore(
          walletDoc.data()!,
          walletDoc.id,
        );
        final newBalance = walletDto.balance + amount.amount;
        final updatedWalletDto = walletDto.withUpdatedBalance(newBalance);

        // Update wallet
        transaction.update(walletRef, updatedWalletDto.toFirestore());

        // Add transaction
        final transactionRef = walletRef
            .collection('transactions')
            .doc(transactionDto.id);
        transaction.set(transactionRef, transactionDto.toFirestore());
      });

      return Right(transactionDto.toDomain());
    } on FirebaseException catch (e) {
      return Left(_mapFirebaseException(e));
    } catch (e) {
      return Left(
        Failure.unknown(message: 'Failed to add funds', exception: e),
      );
    }
  }

  @override
  Future<Either<Failure, domain.Transaction>> deductFunds({
    required String userId,
    required Money amount,
    required String description,
    String? postId,
    domain.TransactionType type = domain.TransactionType.debit,
  }) async {
    try {
      // Create transaction
      final transactionDto = TransactionDto(
        id: _generateTransactionId(),
        type: _transactionTypeToString(type),
        amount: amount.amount,
        currency: amount.currency,
        description: description,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        status: 'completed',
        postId: postId,
      );

      // Update wallet balance and add transaction in a transaction
      await _firestore.runTransaction((transaction) async {
        final walletRef = _firestore.collection('wallets').doc(userId);
        final walletDoc = await transaction.get(walletRef);

        if (!walletDoc.exists) {
          throw Exception('Wallet not found');
        }

        final walletDto = WalletDto.fromFirestore(
          walletDoc.data()!,
          walletDoc.id,
        );

        // Check sufficient funds
        if (walletDto.balance < amount.amount) {
          throw Exception('Insufficient funds');
        }

        final newBalance = walletDto.balance - amount.amount;
        final updatedWalletDto = walletDto.withUpdatedBalance(newBalance);

        // Update wallet
        transaction.update(walletRef, updatedWalletDto.toFirestore());

        // Add transaction
        final transactionRef = walletRef
            .collection('transactions')
            .doc(transactionDto.id);
        transaction.set(transactionRef, transactionDto.toFirestore());
      });

      return Right(transactionDto.toDomain());
    } on FirebaseException catch (e) {
      return Left(_mapFirebaseException(e));
    } catch (e) {
      if (e.toString().contains('Insufficient funds')) {
        return const Left(
          Failure.validation(message: 'Insufficient funds', field: 'amount'),
        );
      }
      return Left(
        Failure.unknown(message: 'Failed to deduct funds', exception: e),
      );
    }
  }

  /// Generate unique transaction ID
  String _generateTransactionId() {
    return 'tx_${DateTime.now().millisecondsSinceEpoch}_${UniqueId.generate().value}';
  }

  /// Convert transaction type to string
  String _transactionTypeToString(domain.TransactionType type) {
    switch (type) {
      case domain.TransactionType.credit:
        return 'credit';
      case domain.TransactionType.debit:
        return 'debit';
      case domain.TransactionType.withdrawal:
        return 'withdrawal';
      case domain.TransactionType.postPayment:
        return 'postpayment';
      case domain.TransactionType.refund:
        return 'refund';
      case domain.TransactionType.fee:
        return 'fee';
    }
  }

  @override
  Future<Either<Failure, List<domain.Transaction>>> getTransactionHistory({
    required String userId,
    int limit = 50,
    String? lastTransactionId,
    List<domain.TransactionType>? types,
    List<domain.TransactionStatus>? statuses,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      Query<Map<String, dynamic>> query = _firestore
          .collection('wallets')
          .doc(userId)
          .collection('transactions')
          .orderBy('createdAt', descending: true);

      if (limit > 0) {
        query = query.limit(limit);
      }

      final snapshot = await query.get();
      final transactions =
          snapshot.docs
              .map((doc) => TransactionDto.fromFirestore(doc.data(), doc.id))
              .map((dto) => dto.toDomain())
              .toList();

      return Right(transactions);
    } on FirebaseException catch (e) {
      return Left(_mapFirebaseException(e));
    } catch (e) {
      return Left(
        Failure.unknown(
          message: 'Failed to get transaction history',
          exception: e,
        ),
      );
    }
  }

  @override
  Future<Either<Failure, bool>> hasSufficientFunds({
    required String userId,
    required Money amount,
  }) async {
    try {
      final walletResult = await getWallet(userId);
      return walletResult.fold((failure) => Left(failure), (wallet) {
        if (wallet == null) {
          return const Right(false);
        }
        return Right(wallet.hasSufficientFunds(amount));
      });
    } catch (e) {
      return Left(
        Failure.unknown(
          message: 'Failed to check sufficient funds',
          exception: e,
        ),
      );
    }
  }

  /// Map Firebase exceptions to domain failures
  Failure _mapFirebaseException(FirebaseException e) {
    switch (e.code) {
      case 'permission-denied':
        return const Failure.permission(message: 'Permission denied');
      case 'not-found':
        return const Failure.notFound(message: 'Resource not found');
      case 'unavailable':
        return const Failure.network(message: 'Service unavailable');
      default:
        return Failure.server(
          message: e.message ?? 'Server error occurred',
          code: e.code,
        );
    }
  }

  // Placeholder implementations for remaining methods
  @override
  Future<Either<Failure, domain.Transaction>> withdrawFunds({
    required String userId,
    required Money amount,
    required String paymentMethodId,
    String? description,
  }) async {
    // TODO: Implement withdrawal logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, domain.Transaction>> processRefund({
    required String userId,
    required String originalTransactionId,
    required Money amount,
    required String reason,
  }) async {
    // TODO: Implement refund logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, domain.Transaction?>> getTransaction({
    required String userId,
    required String transactionId,
  }) async {
    // TODO: Implement get transaction logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, List<domain.Transaction>>> getTransactionsByType({
    required String userId,
    required domain.TransactionType type,
    int limit = 50,
  }) async {
    // TODO: Implement get transactions by type logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, List<domain.Transaction>>> getPendingTransactions({
    required String userId,
  }) async {
    // TODO: Implement get pending transactions logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, domain.Transaction>> updateTransactionStatus({
    required String userId,
    required String transactionId,
    required domain.TransactionStatus status,
    String? failureReason,
  }) async {
    // TODO: Implement update transaction status logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, domain.Transaction>> cancelTransaction({
    required String userId,
    required String transactionId,
    required String reason,
  }) async {
    // TODO: Implement cancel transaction logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, WalletStatistics>> getWalletStatistics({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    // TODO: Implement get wallet statistics logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, Money>> getTotalEarnings({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    // TODO: Implement get total earnings logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, Money>> getTotalSpent({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    // TODO: Implement get total spent logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, List<PaymentMethod>>> getPaymentMethods({
    required String userId,
  }) async {
    // TODO: Implement get payment methods logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, PaymentMethod>> addPaymentMethod({
    required String userId,
    required PaymentMethodType type,
    required String displayName,
    required Map<String, dynamic> paymentData,
  }) async {
    // TODO: Implement add payment method logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, Unit>> removePaymentMethod({
    required String userId,
    required String paymentMethodId,
  }) async {
    // TODO: Implement remove payment method logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, Unit>> setDefaultPaymentMethod({
    required String userId,
    required String paymentMethodId,
  }) async {
    // TODO: Implement set default payment method logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, String>> createPaymentIntent({
    required Money amount,
    required String currency,
    Map<String, dynamic>? metadata,
  }) async {
    // TODO: Implement create payment intent logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, bool>> confirmPayment({
    required String paymentIntentId,
    required String paymentMethodId,
  }) async {
    // TODO: Implement confirm payment logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> processWithdrawal({
    required Money amount,
    required String paymentMethodId,
  }) async {
    // TODO: Implement process withdrawal logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, Map<String, double>>> getExchangeRates({
    required String baseCurrency,
    required List<String> targetCurrencies,
  }) async {
    // TODO: Implement get exchange rates logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, Money>> convertCurrency({
    required Money amount,
    required String targetCurrency,
  }) async {
    // TODO: Implement convert currency logic
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, bool>> validateTransaction({
    required String userId,
    required domain.TransactionType type,
    required Money amount,
    String? paymentMethodId,
  }) async {
    // TODO: Implement validate transaction logic
    return const Right(true); // Temporary implementation
  }

  @override
  Future<Either<Failure, Money>> calculateTransactionFees({
    required domain.TransactionType type,
    required Money amount,
    String? paymentMethodId,
  }) async {
    // TODO: Implement calculate transaction fees logic
    return Right(Money.zero()); // Temporary implementation
  }

  @override
  Future<Either<Failure, Unit>> syncWallet({required String userId}) async {
    // TODO: Implement sync wallet logic
    throw UnimplementedError();
  }
}
