// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wallet_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

WalletDto _$WalletDtoFromJson(Map<String, dynamic> json) {
  return _WalletDto.fromJson(json);
}

/// @nodoc
mixin _$WalletDto {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  double get balance => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  int get createdAt => throw _privateConstructorUsedError;
  int? get updatedAt => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  double? get pendingBalance => throw _privateConstructorUsedError;
  double? get availableBalance => throw _privateConstructorUsedError;
  int? get lastTransactionAt => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this WalletDto to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WalletDto
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WalletDtoCopyWith<WalletDto> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WalletDtoCopyWith<$Res> {
  factory $WalletDtoCopyWith(WalletDto value, $Res Function(WalletDto) then) =
      _$WalletDtoCopyWithImpl<$Res, WalletDto>;
  @useResult
  $Res call({
    String id,
    String userId,
    double balance,
    String currency,
    int createdAt,
    int? updatedAt,
    bool isActive,
    double? pendingBalance,
    double? availableBalance,
    int? lastTransactionAt,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$WalletDtoCopyWithImpl<$Res, $Val extends WalletDto>
    implements $WalletDtoCopyWith<$Res> {
  _$WalletDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WalletDto
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? balance = null,
    Object? currency = null,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? isActive = null,
    Object? pendingBalance = freezed,
    Object? availableBalance = freezed,
    Object? lastTransactionAt = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id:
                null == id
                    ? _value.id
                    : id // ignore: cast_nullable_to_non_nullable
                        as String,
            userId:
                null == userId
                    ? _value.userId
                    : userId // ignore: cast_nullable_to_non_nullable
                        as String,
            balance:
                null == balance
                    ? _value.balance
                    : balance // ignore: cast_nullable_to_non_nullable
                        as double,
            currency:
                null == currency
                    ? _value.currency
                    : currency // ignore: cast_nullable_to_non_nullable
                        as String,
            createdAt:
                null == createdAt
                    ? _value.createdAt
                    : createdAt // ignore: cast_nullable_to_non_nullable
                        as int,
            updatedAt:
                freezed == updatedAt
                    ? _value.updatedAt
                    : updatedAt // ignore: cast_nullable_to_non_nullable
                        as int?,
            isActive:
                null == isActive
                    ? _value.isActive
                    : isActive // ignore: cast_nullable_to_non_nullable
                        as bool,
            pendingBalance:
                freezed == pendingBalance
                    ? _value.pendingBalance
                    : pendingBalance // ignore: cast_nullable_to_non_nullable
                        as double?,
            availableBalance:
                freezed == availableBalance
                    ? _value.availableBalance
                    : availableBalance // ignore: cast_nullable_to_non_nullable
                        as double?,
            lastTransactionAt:
                freezed == lastTransactionAt
                    ? _value.lastTransactionAt
                    : lastTransactionAt // ignore: cast_nullable_to_non_nullable
                        as int?,
            metadata:
                freezed == metadata
                    ? _value.metadata
                    : metadata // ignore: cast_nullable_to_non_nullable
                        as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$WalletDtoImplCopyWith<$Res>
    implements $WalletDtoCopyWith<$Res> {
  factory _$$WalletDtoImplCopyWith(
    _$WalletDtoImpl value,
    $Res Function(_$WalletDtoImpl) then,
  ) = __$$WalletDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String userId,
    double balance,
    String currency,
    int createdAt,
    int? updatedAt,
    bool isActive,
    double? pendingBalance,
    double? availableBalance,
    int? lastTransactionAt,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$WalletDtoImplCopyWithImpl<$Res>
    extends _$WalletDtoCopyWithImpl<$Res, _$WalletDtoImpl>
    implements _$$WalletDtoImplCopyWith<$Res> {
  __$$WalletDtoImplCopyWithImpl(
    _$WalletDtoImpl _value,
    $Res Function(_$WalletDtoImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of WalletDto
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? balance = null,
    Object? currency = null,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? isActive = null,
    Object? pendingBalance = freezed,
    Object? availableBalance = freezed,
    Object? lastTransactionAt = freezed,
    Object? metadata = freezed,
  }) {
    return _then(
      _$WalletDtoImpl(
        id:
            null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                    as String,
        userId:
            null == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                    as String,
        balance:
            null == balance
                ? _value.balance
                : balance // ignore: cast_nullable_to_non_nullable
                    as double,
        currency:
            null == currency
                ? _value.currency
                : currency // ignore: cast_nullable_to_non_nullable
                    as String,
        createdAt:
            null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                    as int,
        updatedAt:
            freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                    as int?,
        isActive:
            null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                    as bool,
        pendingBalance:
            freezed == pendingBalance
                ? _value.pendingBalance
                : pendingBalance // ignore: cast_nullable_to_non_nullable
                    as double?,
        availableBalance:
            freezed == availableBalance
                ? _value.availableBalance
                : availableBalance // ignore: cast_nullable_to_non_nullable
                    as double?,
        lastTransactionAt:
            freezed == lastTransactionAt
                ? _value.lastTransactionAt
                : lastTransactionAt // ignore: cast_nullable_to_non_nullable
                    as int?,
        metadata:
            freezed == metadata
                ? _value._metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                    as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$WalletDtoImpl extends _WalletDto {
  const _$WalletDtoImpl({
    required this.id,
    required this.userId,
    required this.balance,
    required this.currency,
    required this.createdAt,
    this.updatedAt,
    this.isActive = true,
    this.pendingBalance,
    this.availableBalance,
    this.lastTransactionAt,
    final Map<String, dynamic>? metadata,
  }) : _metadata = metadata,
       super._();

  factory _$WalletDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$WalletDtoImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final double balance;
  @override
  final String currency;
  @override
  final int createdAt;
  @override
  final int? updatedAt;
  @override
  @JsonKey()
  final bool isActive;
  @override
  final double? pendingBalance;
  @override
  final double? availableBalance;
  @override
  final int? lastTransactionAt;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'WalletDto(id: $id, userId: $userId, balance: $balance, currency: $currency, createdAt: $createdAt, updatedAt: $updatedAt, isActive: $isActive, pendingBalance: $pendingBalance, availableBalance: $availableBalance, lastTransactionAt: $lastTransactionAt, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WalletDtoImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.pendingBalance, pendingBalance) ||
                other.pendingBalance == pendingBalance) &&
            (identical(other.availableBalance, availableBalance) ||
                other.availableBalance == availableBalance) &&
            (identical(other.lastTransactionAt, lastTransactionAt) ||
                other.lastTransactionAt == lastTransactionAt) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    userId,
    balance,
    currency,
    createdAt,
    updatedAt,
    isActive,
    pendingBalance,
    availableBalance,
    lastTransactionAt,
    const DeepCollectionEquality().hash(_metadata),
  );

  /// Create a copy of WalletDto
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WalletDtoImplCopyWith<_$WalletDtoImpl> get copyWith =>
      __$$WalletDtoImplCopyWithImpl<_$WalletDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WalletDtoImplToJson(this);
  }
}

abstract class _WalletDto extends WalletDto {
  const factory _WalletDto({
    required final String id,
    required final String userId,
    required final double balance,
    required final String currency,
    required final int createdAt,
    final int? updatedAt,
    final bool isActive,
    final double? pendingBalance,
    final double? availableBalance,
    final int? lastTransactionAt,
    final Map<String, dynamic>? metadata,
  }) = _$WalletDtoImpl;
  const _WalletDto._() : super._();

  factory _WalletDto.fromJson(Map<String, dynamic> json) =
      _$WalletDtoImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  double get balance;
  @override
  String get currency;
  @override
  int get createdAt;
  @override
  int? get updatedAt;
  @override
  bool get isActive;
  @override
  double? get pendingBalance;
  @override
  double? get availableBalance;
  @override
  int? get lastTransactionAt;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of WalletDto
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WalletDtoImplCopyWith<_$WalletDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
