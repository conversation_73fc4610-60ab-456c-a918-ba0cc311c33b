import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../core/domain/entities.dart';
import '../../domain/entities/transaction.dart';

part 'transaction_dto.freezed.dart';
part 'transaction_dto.g.dart';

/// Data Transfer Object for Transaction entity
@freezed
class TransactionDto with _$TransactionDto {
  const factory TransactionDto({
    required String id,
    required String type,
    required double amount,
    required String currency,
    required String description,
    required int createdAt,
    required String status,
    int? updatedAt,
    int? completedAt,
    String? postId,
    String? paymentIntentId,
    String? withdrawalId,
    String? failureReason,
    Map<String, dynamic>? metadata,
  }) = _TransactionDto;

  const TransactionDto._();

  /// Create from JSON
  factory TransactionDto.fromJson(Map<String, dynamic> json) => 
      _$TransactionDtoFromJson(json);

  /// Create from Firestore document
  factory TransactionDto.fromFirestore(Map<String, dynamic> data, String id) {
    return TransactionDto(
      id: id,
      type: data['type'] ?? 'unknown',
      amount: (data['amount'] as num?)?.toDouble() ?? 0.0,
      currency: data['currency'] ?? 'USD',
      description: data['description'] ?? '',
      createdAt: data['createdAt'] ?? DateTime.now().millisecondsSinceEpoch,
      status: data['status'] ?? 'completed',
      updatedAt: data['updatedAt'],
      completedAt: data['completedAt'],
      postId: data['postId'],
      paymentIntentId: data['paymentIntentId'],
      withdrawalId: data['withdrawalId'],
      failureReason: data['failureReason'],
      metadata: data['metadata'],
    );
  }

  /// Create from legacy Transaction model
  factory TransactionDto.fromLegacy(dynamic legacyTransaction) {
    return TransactionDto(
      id: legacyTransaction.id ?? '',
      type: _mapLegacyType(legacyTransaction.type ?? 'unknown'),
      amount: (legacyTransaction.amount as num?)?.toDouble() ?? 0.0,
      currency: 'USD',
      description: legacyTransaction.description ?? '',
      createdAt: legacyTransaction.timestamp?.millisecondsSinceEpoch ?? 
                 DateTime.now().millisecondsSinceEpoch,
      status: _mapLegacyStatus(legacyTransaction.status ?? 'completed'),
      postId: legacyTransaction.postId,
    );
  }

  /// Convert to domain entity
  Transaction toDomain() {
    return Transaction(
      id: id,
      type: _parseTransactionType(type),
      amount: Money(amount: amount, currency: currency),
      description: description,
      createdAt: DateTime.fromMillisecondsSinceEpoch(createdAt),
      status: _parseTransactionStatus(status),
      updatedAt: updatedAt != null 
          ? DateTime.fromMillisecondsSinceEpoch(updatedAt!)
          : null,
      completedAt: completedAt != null 
          ? DateTime.fromMillisecondsSinceEpoch(completedAt!)
          : null,
      postId: postId,
      paymentIntentId: paymentIntentId,
      withdrawalId: withdrawalId,
      failureReason: failureReason,
      metadata: metadata,
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'type': type,
      'amount': amount,
      'currency': currency,
      'description': description,
      'createdAt': createdAt,
      'status': status,
      'updatedAt': updatedAt,
      'completedAt': completedAt,
      'postId': postId,
      'paymentIntentId': paymentIntentId,
      'withdrawalId': withdrawalId,
      'failureReason': failureReason,
      'metadata': metadata,
    };
  }

  /// Parse transaction type from string
  static TransactionType _parseTransactionType(String type) {
    switch (type.toLowerCase()) {
      case 'credit':
        return TransactionType.credit;
      case 'debit':
        return TransactionType.debit;
      case 'withdrawal':
        return TransactionType.withdrawal;
      case 'post_payment':
      case 'postpayment':
        return TransactionType.postPayment;
      case 'refund':
        return TransactionType.refund;
      case 'fee':
        return TransactionType.fee;
      default:
        return TransactionType.debit;
    }
  }

  /// Parse transaction status from string
  static TransactionStatus _parseTransactionStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return TransactionStatus.pending;
      case 'completed':
        return TransactionStatus.completed;
      case 'failed':
        return TransactionStatus.failed;
      case 'cancelled':
        return TransactionStatus.cancelled;
      case 'processing':
        return TransactionStatus.processing;
      default:
        return TransactionStatus.completed;
    }
  }

  /// Map legacy transaction type
  static String _mapLegacyType(String legacyType) {
    switch (legacyType.toLowerCase()) {
      case 'post_payment':
        return 'postpayment';
      default:
        return legacyType.toLowerCase();
    }
  }

  /// Map legacy transaction status
  static String _mapLegacyStatus(String legacyStatus) {
    return legacyStatus.toLowerCase();
  }
}

/// Extension to convert domain Transaction to DTO
extension TransactionToDto on Transaction {
  TransactionDto toDto() {
    return TransactionDto(
      id: id,
      type: _transactionTypeToString(type),
      amount: amount.amount,
      currency: amount.currency,
      description: description,
      createdAt: createdAt.millisecondsSinceEpoch,
      status: _transactionStatusToString(status),
      updatedAt: updatedAt?.millisecondsSinceEpoch,
      completedAt: completedAt?.millisecondsSinceEpoch,
      postId: postId,
      paymentIntentId: paymentIntentId,
      withdrawalId: withdrawalId,
      failureReason: failureReason,
      metadata: metadata,
    );
  }

  /// Convert transaction type to string
  String _transactionTypeToString(TransactionType type) {
    switch (type) {
      case TransactionType.credit:
        return 'credit';
      case TransactionType.debit:
        return 'debit';
      case TransactionType.withdrawal:
        return 'withdrawal';
      case TransactionType.postPayment:
        return 'postpayment';
      case TransactionType.refund:
        return 'refund';
      case TransactionType.fee:
        return 'fee';
    }
  }

  /// Convert transaction status to string
  String _transactionStatusToString(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.pending:
        return 'pending';
      case TransactionStatus.completed:
        return 'completed';
      case TransactionStatus.failed:
        return 'failed';
      case TransactionStatus.cancelled:
        return 'cancelled';
      case TransactionStatus.processing:
        return 'processing';
    }
  }
}
