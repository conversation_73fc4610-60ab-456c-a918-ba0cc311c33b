// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TransactionDtoImpl _$$TransactionDtoImplFromJson(Map<String, dynamic> json) =>
    _$TransactionDtoImpl(
      id: json['id'] as String,
      type: json['type'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      description: json['description'] as String,
      createdAt: (json['createdAt'] as num).toInt(),
      status: json['status'] as String,
      updatedAt: (json['updatedAt'] as num?)?.toInt(),
      completedAt: (json['completedAt'] as num?)?.toInt(),
      postId: json['postId'] as String?,
      paymentIntentId: json['paymentIntentId'] as String?,
      withdrawalId: json['withdrawalId'] as String?,
      failureReason: json['failureReason'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$TransactionDtoImplToJson(
  _$TransactionDtoImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'type': instance.type,
  'amount': instance.amount,
  'currency': instance.currency,
  'description': instance.description,
  'createdAt': instance.createdAt,
  'status': instance.status,
  if (instance.updatedAt case final value?) 'updatedAt': value,
  if (instance.completedAt case final value?) 'completedAt': value,
  if (instance.postId case final value?) 'postId': value,
  if (instance.paymentIntentId case final value?) 'paymentIntentId': value,
  if (instance.withdrawalId case final value?) 'withdrawalId': value,
  if (instance.failureReason case final value?) 'failureReason': value,
  if (instance.metadata case final value?) 'metadata': value,
};
