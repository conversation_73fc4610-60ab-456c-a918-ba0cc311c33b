import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../core/domain/entities.dart';
import '../../domain/entities/wallet.dart';

part 'wallet_dto.freezed.dart';
part 'wallet_dto.g.dart';

/// Data Transfer Object for Wallet entity
@freezed
class WalletDto with _$WalletDto {
  const factory WalletDto({
    required String id,
    required String userId,
    required double balance,
    required String currency,
    required int createdAt,
    int? updatedAt,
    @Default(true) bool isActive,
    double? pendingBalance,
    double? availableBalance,
    int? lastTransactionAt,
    Map<String, dynamic>? metadata,
  }) = _WalletDto;

  const WalletDto._();

  /// Create from JSON
  factory WalletDto.fromJson(Map<String, dynamic> json) => 
      _$WalletDtoFromJson(json);

  /// Create from Firestore document
  factory WalletDto.fromFirestore(Map<String, dynamic> data, String id) {
    return WalletDto(
      id: id,
      userId: data['userId'] ?? '',
      balance: (data['balance'] as num?)?.toDouble() ?? 0.0,
      currency: data['currency'] ?? 'USD',
      createdAt: data['createdAt'] ?? DateTime.now().millisecondsSinceEpoch,
      updatedAt: data['updatedAt'],
      isActive: data['isActive'] ?? true,
      pendingBalance: (data['pendingBalance'] as num?)?.toDouble(),
      availableBalance: (data['availableBalance'] as num?)?.toDouble(),
      lastTransactionAt: data['lastTransactionAt'],
      metadata: data['metadata'],
    );
  }

  /// Create from legacy wallet data
  factory WalletDto.fromLegacy({
    required String userId,
    required double balance,
    String currency = 'USD',
  }) {
    final now = DateTime.now().millisecondsSinceEpoch;
    return WalletDto(
      id: userId, // Use userId as wallet ID for legacy compatibility
      userId: userId,
      balance: balance,
      currency: currency,
      createdAt: now,
      updatedAt: now,
      isActive: true,
    );
  }

  /// Convert to domain entity
  Wallet toDomain() {
    return Wallet(
      id: id,
      userId: userId,
      balance: Money(amount: balance, currency: currency),
      createdAt: DateTime.fromMillisecondsSinceEpoch(createdAt),
      updatedAt: updatedAt != null 
          ? DateTime.fromMillisecondsSinceEpoch(updatedAt!)
          : null,
      isActive: isActive,
      currency: currency,
      pendingBalance: pendingBalance != null 
          ? Money(amount: pendingBalance!, currency: currency)
          : null,
      availableBalance: availableBalance != null 
          ? Money(amount: availableBalance!, currency: currency)
          : null,
      lastTransactionAt: lastTransactionAt != null 
          ? DateTime.fromMillisecondsSinceEpoch(lastTransactionAt!)
          : null,
      metadata: metadata,
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'balance': balance,
      'currency': currency,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isActive': isActive,
      'pendingBalance': pendingBalance,
      'availableBalance': availableBalance,
      'lastTransactionAt': lastTransactionAt,
      'metadata': metadata,
    };
  }

  /// Create a copy with updated balance
  WalletDto withUpdatedBalance(double newBalance) => copyWith(
    balance: newBalance,
    updatedAt: DateTime.now().millisecondsSinceEpoch,
    lastTransactionAt: DateTime.now().millisecondsSinceEpoch,
  );

  /// Create a copy with updated pending balance
  WalletDto withPendingBalance(double? pending) => copyWith(
    pendingBalance: pending,
    updatedAt: DateTime.now().millisecondsSinceEpoch,
  );

  /// Create a copy with updated available balance
  WalletDto withAvailableBalance(double? available) => copyWith(
    availableBalance: available,
    updatedAt: DateTime.now().millisecondsSinceEpoch,
  );

  /// Create a copy with updated active status
  WalletDto withActiveStatus(bool active) => copyWith(
    isActive: active,
    updatedAt: DateTime.now().millisecondsSinceEpoch,
  );
}

/// Extension to convert domain Wallet to DTO
extension WalletToDto on Wallet {
  WalletDto toDto() {
    return WalletDto(
      id: id,
      userId: userId,
      balance: balance.amount,
      currency: currency,
      createdAt: createdAt.millisecondsSinceEpoch,
      updatedAt: updatedAt?.millisecondsSinceEpoch,
      isActive: isActive,
      pendingBalance: pendingBalance?.amount,
      availableBalance: availableBalance?.amount,
      lastTransactionAt: lastTransactionAt?.millisecondsSinceEpoch,
      metadata: metadata,
    );
  }
}
