// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wallet_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WalletDtoImpl _$$WalletDtoImplFromJson(Map<String, dynamic> json) =>
    _$WalletDtoImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      balance: (json['balance'] as num).toDouble(),
      currency: json['currency'] as String,
      createdAt: (json['createdAt'] as num).toInt(),
      updatedAt: (json['updatedAt'] as num?)?.toInt(),
      isActive: json['isActive'] as bool? ?? true,
      pendingBalance: (json['pendingBalance'] as num?)?.toDouble(),
      availableBalance: (json['availableBalance'] as num?)?.toDouble(),
      lastTransactionAt: (json['lastTransactionAt'] as num?)?.toInt(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$WalletDtoImplToJson(
  _$WalletDtoImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'userId': instance.userId,
  'balance': instance.balance,
  'currency': instance.currency,
  'createdAt': instance.createdAt,
  if (instance.updatedAt case final value?) 'updatedAt': value,
  'isActive': instance.isActive,
  if (instance.pendingBalance case final value?) 'pendingBalance': value,
  if (instance.availableBalance case final value?) 'availableBalance': value,
  if (instance.lastTransactionAt case final value?) 'lastTransactionAt': value,
  if (instance.metadata case final value?) 'metadata': value,
};
