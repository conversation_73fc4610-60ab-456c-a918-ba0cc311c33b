import '../../../../core/domain/entities.dart';
import '../../../../services/payment_service.dart';

/// Adapter to bridge the new wallet architecture with the existing PaymentService
class PaymentServiceAdapter {
  final PaymentService _paymentService;

  PaymentServiceAdapter(this._paymentService);

  /// Process payment using the legacy payment service
  Future<bool> processPayment({
    required Money amount,
    required String paymentMethodId,
  }) async {
    try {
      // Convert to legacy format and process payment
      final result = await _paymentService.processPayment(
        amount: amount.amount,
        currency: amount.currency,
        paymentMethodId: paymentMethodId,
      );
      
      return result;
    } catch (e) {
      return false;
    }
  }

  /// Process withdrawal using the legacy payment service
  Future<Map<String, dynamic>?> processWithdrawal({
    required Money amount,
    required String paymentMethodId,
  }) async {
    try {
      // Convert to legacy format and process withdrawal
      final result = await _paymentService.processWithdrawal(
        amount: amount.amount,
        paymentMethodId: paymentMethodId,
      );
      
      return result;
    } catch (e) {
      return null;
    }
  }

  /// Create payment intent using the legacy payment service
  Future<String?> createPaymentIntent({
    required Money amount,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final result = await _paymentService.createPaymentIntent(
        amount: amount.cents, // Convert to cents
        currency: amount.currency,
        metadata: metadata,
      );
      
      return result;
    } catch (e) {
      return null;
    }
  }

  /// Confirm payment using the legacy payment service
  Future<bool> confirmPayment({
    required String paymentIntentId,
    required String paymentMethodId,
  }) async {
    try {
      final result = await _paymentService.confirmPayment(
        paymentIntentId: paymentIntentId,
        paymentMethodId: paymentMethodId,
      );
      
      return result;
    } catch (e) {
      return false;
    }
  }

  /// Calculate transaction fees using the legacy payment service
  Future<Money> calculateFees({
    required Money amount,
    required String transactionType,
    String? paymentMethodId,
  }) async {
    try {
      final feeAmount = await _paymentService.calculateFees(
        amount: amount.amount,
        transactionType: transactionType,
        paymentMethodId: paymentMethodId,
      );
      
      return Money(amount: feeAmount, currency: amount.currency);
    } catch (e) {
      return Money.zero();
    }
  }

  /// Validate payment method using the legacy payment service
  Future<bool> validatePaymentMethod(String paymentMethodId) async {
    try {
      return await _paymentService.validatePaymentMethod(paymentMethodId);
    } catch (e) {
      return false;
    }
  }
}
