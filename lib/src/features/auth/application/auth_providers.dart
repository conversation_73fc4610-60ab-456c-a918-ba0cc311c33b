import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/core_providers.dart';
import '../data/repositories/firebase_auth_repository.dart';
import '../domain/repositories/auth_repository.dart';
import '../domain/usecases/get_current_user_usecase.dart';
import '../domain/usecases/sign_in_usecase.dart';
import '../domain/usecases/sign_out_usecase.dart';
import '../domain/usecases/sign_up_usecase.dart';
import 'auth_notifier.dart';
import 'auth_state.dart';

/// Auth repository provider
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  final firebaseAuth = ref.watch(firebaseAuthProvider);
  final firestore = ref.watch(firestoreProvider);
  
  return FirebaseAuthRepository(
    firebaseAuth: firebaseAuth,
    firestore: firestore,
  );
});

/// Get current user use case provider
final getCurrentUserUseCaseProvider = Provider<GetCurrentUserUseCase>((ref) {
  final repository = ref.watch(authRepositoryProvider);
  return GetCurrentUserUseCase(repository);
});

/// Sign in use case provider
final signInUseCaseProvider = Provider<SignInUseCase>((ref) {
  final repository = ref.watch(authRepositoryProvider);
  return SignInUseCase(repository);
});

/// Sign up use case provider
final signUpUseCaseProvider = Provider<SignUpUseCase>((ref) {
  final repository = ref.watch(authRepositoryProvider);
  return SignUpUseCase(repository);
});

/// Sign out use case provider
final signOutUseCaseProvider = Provider<SignOutUseCase>((ref) {
  final repository = ref.watch(authRepositoryProvider);
  return SignOutUseCase(repository);
});

/// Auth notifier provider
final authNotifierProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final repository = ref.watch(authRepositoryProvider);
  final getCurrentUserUseCase = ref.watch(getCurrentUserUseCaseProvider);
  final signInUseCase = ref.watch(signInUseCaseProvider);
  final signUpUseCase = ref.watch(signUpUseCaseProvider);
  final signOutUseCase = ref.watch(signOutUseCaseProvider);
  
  return AuthNotifier(
    repository: repository,
    getCurrentUserUseCase: getCurrentUserUseCase,
    signInUseCase: signInUseCase,
    signUpUseCase: signUpUseCase,
    signOutUseCase: signOutUseCase,
  );
});

/// Current auth state provider
final authStateProvider = Provider<AuthState>((ref) {
  return ref.watch(authNotifierProvider);
});

/// Current user provider
final currentUserProvider = Provider((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.user;
});

/// Is authenticated provider
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.isAuthenticated;
});

/// Is loading provider
final isAuthLoadingProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.isLoading;
});

/// Has completed onboarding provider
final hasCompletedOnboardingProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.hasCompletedOnboarding;
});

/// Can create posts provider
final canCreatePostsProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.canCreatePosts;
});

/// Is email verified provider
final isEmailVerifiedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.isEmailVerified;
});

/// Is profile completed provider
final isProfileCompletedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.isProfileCompleted;
});

/// Auth error provider
final authErrorProvider = Provider((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.error;
});

/// Has auth error provider
final hasAuthErrorProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.hasError;
});
