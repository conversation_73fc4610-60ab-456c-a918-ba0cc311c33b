import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/errors/failures.dart';
import 'auth_providers.dart';
import 'auth_state.dart';

/// Widget that handles authentication routing based on auth state
class AuthWrapper extends ConsumerWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);

    return authState.when(
      initial: () => const SplashScreen(),
      loading: () => const SplashScreen(),
      authenticated: (user) {
        // Check if user has completed onboarding
        if (user.hasCompletedOnboarding) {
          return const Scaffold(
            body: Center(child: Text('Main Navigation - TODO: Integrate')),
          );
        } else if (user.username.value.isEmpty) {
          return const Scaffold(
            body: Center(child: Text('Choose Username - TODO: Integrate')),
          );
        } else {
          return const Scaffold(
            body: Center(child: Text('Create Profile - TODO: Integrate')),
          );
        }
      },
      unauthenticated: () {
        // Show different screens based on platform
        if (kIsWeb) {
          return const Scaffold(
            body: Center(child: Text('Landing Page - TODO: Integrate')),
          );
        } else {
          return const Scaffold(
            body: Center(child: Text('Sign Up - TODO: Integrate')),
          );
        }
      },
      error: (failure, user) {
        // If we have a user but there's an error, try to continue
        if (user != null && user.hasCompletedOnboarding) {
          return const Scaffold(
            body: Center(child: Text('Main Navigation - TODO: Integrate')),
          );
        }

        // Otherwise show error and fallback to unauthenticated state
        return ErrorScreen(
          error: failure.debugMessage,
          onRetry: () {
            ref.read(authNotifierProvider.notifier).clearError();
          },
        );
      },
    );
  }
}

/// Splash screen shown during loading states
class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading...'),
          ],
        ),
      ),
    );
  }
}

/// Error screen shown when authentication fails
class ErrorScreen extends StatelessWidget {
  final String error;
  final VoidCallback? onRetry;

  const ErrorScreen({super.key, required this.error, this.onRetry});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'Authentication Error',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                error,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 24),
              if (onRetry != null)
                ElevatedButton(onPressed: onRetry, child: const Text('Retry')),
            ],
          ),
        ),
      ),
    );
  }
}

// Note: The actual screen imports will be handled by the main app
// These are just references to the existing screens
