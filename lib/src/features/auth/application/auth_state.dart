import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../core/errors/failures.dart';
import '../domain/entities/user.dart';

part 'auth_state.freezed.dart';

/// Authentication state for the application
@freezed
class AuthState with _$AuthState {
  const factory AuthState.initial() = _Initial;
  
  const factory AuthState.loading() = _Loading;
  
  const factory AuthState.authenticated({
    required User user,
  }) = _Authenticated;
  
  const factory AuthState.unauthenticated() = _Unauthenticated;
  
  const factory AuthState.error({
    required Failure failure,
    User? user,
  }) = _Error;
}

/// Extension methods for AuthState
extension AuthStateExtension on AuthState {
  /// Check if user is authenticated
  bool get isAuthenticated => maybeWhen(
    authenticated: (_) => true,
    orElse: () => false,
  );

  /// Check if authentication is in progress
  bool get isLoading => maybeWhen(
    loading: () => true,
    orElse: () => false,
  );

  /// Check if there's an error
  bool get hasError => maybeWhen(
    error: (_, __) => true,
    orElse: () => false,
  );

  /// Get the current user if authenticated
  User? get user => maybeWhen(
    authenticated: (user) => user,
    error: (_, user) => user,
    orElse: () => null,
  );

  /// Get the current error if any
  Failure? get error => maybeWhen(
    error: (failure, _) => failure,
    orElse: () => null,
  );

  /// Check if user has completed onboarding
  bool get hasCompletedOnboarding => user?.hasCompletedOnboarding ?? false;

  /// Check if user can create posts
  bool get canCreatePosts => user?.canCreatePosts ?? false;

  /// Check if email is verified
  bool get isEmailVerified => user?.emailVerified ?? false;

  /// Check if profile is completed
  bool get isProfileCompleted => user?.profileCompleted ?? false;
}
