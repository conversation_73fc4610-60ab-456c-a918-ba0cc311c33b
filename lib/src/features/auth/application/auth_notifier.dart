import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/domain/entities.dart';
import '../../../core/errors/failures.dart';
import '../domain/entities/user.dart';
import '../domain/repositories/auth_repository.dart';
import '../domain/usecases/get_current_user_usecase.dart';
import '../domain/usecases/sign_in_usecase.dart';
import '../domain/usecases/sign_out_usecase.dart';
import '../domain/usecases/sign_up_usecase.dart';
import 'auth_state.dart';

/// Notifier for managing authentication state
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthRepository _repository;
  final GetCurrentUserUseCase _getCurrentUserUseCase;
  final SignInUseCase _signInUseCase;
  final SignUpUseCase _signUpUseCase;
  final SignOutUseCase _signOutUseCase;

  StreamSubscription<User?>? _authStateSubscription;

  AuthNotifier({
    required AuthRepository repository,
    required GetCurrentUserUseCase getCurrentUserUseCase,
    required SignInUseCase signInUseCase,
    required SignUpUseCase signUpUseCase,
    required SignOutUseCase signOutUseCase,
  }) : _repository = repository,
       _getCurrentUserUseCase = getCurrentUserUseCase,
       _signInUseCase = signInUseCase,
       _signUpUseCase = signUpUseCase,
       _signOutUseCase = signOutUseCase,
       super(const AuthState.initial()) {
    _initializeAuthState();
  }

  /// Initialize authentication state and listen to changes
  void _initializeAuthState() {
    _authStateSubscription = _getCurrentUserUseCase.authStateChanges.listen(
      (user) {
        if (user != null) {
          state = AuthState.authenticated(user: user);
        } else {
          state = const AuthState.unauthenticated();
        }
      },
      onError: (error) {
        state = AuthState.error(
          failure:
              error is Failure
                  ? error
                  : Failure.unknown(
                    message: 'Unknown authentication error',
                    exception: error,
                  ),
        );
      },
    );
  }

  /// Sign in with email and password
  Future<void> signIn({required String email, required String password}) async {
    if (state.isLoading) return;

    state = const AuthState.loading();

    try {
      final emailAddress = EmailAddress(email);
      final params = SignInParams(email: emailAddress, password: password);

      final result = await _signInUseCase(params);

      result.fold(
        (failure) => state = AuthState.error(failure: failure),
        (user) => state = AuthState.authenticated(user: user),
      );
    } catch (e) {
      state = AuthState.error(
        failure: Failure.validation(
          message: 'Invalid email format: $e',
          field: 'email',
        ),
      );
    }
  }

  /// Sign up with email and password
  Future<void> signUp({
    required String email,
    required String password,
    required String username,
    required String displayName,
  }) async {
    if (state.isLoading) return;

    state = const AuthState.loading();

    try {
      final emailAddress = EmailAddress(email);
      final usernameValue = Username(username);
      final params = SignUpParams(
        email: emailAddress,
        password: password,
        username: usernameValue,
        displayName: displayName,
      );

      final result = await _signUpUseCase(params);

      result.fold(
        (failure) => state = AuthState.error(failure: failure),
        (user) => state = AuthState.authenticated(user: user),
      );
    } catch (e) {
      state = AuthState.error(
        failure: Failure.validation(message: 'Invalid input format: $e'),
      );
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    if (state.isLoading) return;

    state = const AuthState.loading();

    final result = await _signOutUseCase();

    result.fold(
      (failure) => state = AuthState.error(failure: failure),
      (_) => state = const AuthState.unauthenticated(),
    );
  }

  /// Send email verification
  Future<void> sendEmailVerification() async {
    final result = await _repository.sendEmailVerification();

    result.fold(
      (failure) => state = AuthState.error(failure: failure, user: state.user),
      (_) {
        // Keep current state but could show a success message
      },
    );
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      final emailAddress = EmailAddress(email);
      final result = await _repository.sendPasswordResetEmail(
        email: emailAddress,
      );

      result.fold((failure) => state = AuthState.error(failure: failure), (_) {
        // Password reset email sent successfully
      });
    } catch (e) {
      state = AuthState.error(
        failure: Failure.validation(
          message: 'Invalid email format: $e',
          field: 'email',
        ),
      );
    }
  }

  /// Check if email is verified
  Future<bool> checkEmailVerification() async {
    final result = await _repository.isEmailVerified();

    return result.fold(
      (failure) {
        state = AuthState.error(failure: failure, user: state.user);
        return false;
      },
      (isVerified) {
        if (isVerified && state.user != null) {
          // Update user with verified email
          final updatedUser = state.user!.withVerifiedEmail();
          state = AuthState.authenticated(user: updatedUser);
        }
        return isVerified;
      },
    );
  }

  /// Refresh current user data
  Future<void> refreshUser() async {
    final currentUser = state.user;
    if (currentUser == null) return;

    final result = await _repository.refreshUserData(currentUser.id);

    result.fold(
      (failure) => state = AuthState.error(failure: failure, user: currentUser),
      (user) => state = AuthState.authenticated(user: user),
    );
  }

  /// Clear error state
  void clearError() {
    state.maybeWhen(
      error: (failure, user) {
        if (user != null) {
          state = AuthState.authenticated(user: user);
        } else {
          state = const AuthState.unauthenticated();
        }
      },
      orElse: () {},
    );
  }

  @override
  void dispose() {
    _authStateSubscription?.cancel();
    super.dispose();
  }
}
