import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dartz/dartz.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import '../../../../core/errors/failures.dart';
import '../../../../core/domain/entities.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../models/user_dto.dart';

/// Firebase implementation of AuthRepository
class FirebaseAuthRepository implements AuthRepository {
  final firebase_auth.FirebaseAuth _firebaseAuth;
  final FirebaseFirestore _firestore;

  FirebaseAuthRepository({
    required firebase_auth.FirebaseAuth firebaseAuth,
    required FirebaseFirestore firestore,
  }) : _firebaseAuth = firebaseAuth,
       _firestore = firestore;

  @override
  Stream<User?> get authStateChanges {
    return _firebaseAuth.authStateChanges().asyncMap((firebaseUser) async {
      if (firebaseUser == null) return null;

      try {
        final userDoc =
            await _firestore.collection('users').doc(firebaseUser.uid).get();

        final userDto = UserDto.fromFirebaseUser(
          firebaseUser,
          userDoc.exists ? userDoc.data() : null,
        );

        return userDto.toDomain();
      } catch (e) {
        return null;
      }
    });
  }

  @override
  Future<Either<Failure, User?>> getCurrentUser() async {
    try {
      final firebaseUser = _firebaseAuth.currentUser;
      if (firebaseUser == null) return const Right(null);

      final userDoc =
          await _firestore.collection('users').doc(firebaseUser.uid).get();

      final userDto = UserDto.fromFirebaseUser(
        firebaseUser,
        userDoc.exists ? userDoc.data() : null,
      );

      return Right(userDto.toDomain());
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(
        Failure.unknown(message: 'Failed to get current user', exception: e),
      );
    }
  }

  @override
  Future<Either<Failure, User>> signUpWithEmailAndPassword({
    required EmailAddress email,
    required String password,
    required Username username,
    required String displayName,
  }) async {
    try {
      // Create Firebase Auth user
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email.value,
        password: password,
      );

      final firebaseUser = credential.user;
      if (firebaseUser == null) {
        return const Left(
          Failure.auth(message: 'Failed to create user account'),
        );
      }

      // Create user document in Firestore
      final now = DateTime.now();
      final userDto = UserDto(
        id: firebaseUser.uid,
        email: email.value,
        username: username.value,
        displayName: displayName,
        emailVerified: firebaseUser.emailVerified,
        profileCompleted: false,
        createdAt: now,
        updatedAt: now,
      );

      await _firestore
          .collection('users')
          .doc(firebaseUser.uid)
          .set(userDto.toFirestore());

      return Right(userDto.toDomain());
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(
        Failure.unknown(message: 'Failed to sign up user', exception: e),
      );
    }
  }

  @override
  Future<Either<Failure, User>> signInWithEmailAndPassword({
    required EmailAddress email,
    required String password,
  }) async {
    try {
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email.value,
        password: password,
      );

      final firebaseUser = credential.user;
      if (firebaseUser == null) {
        return const Left(Failure.auth(message: 'Failed to sign in user'));
      }

      final userDoc =
          await _firestore.collection('users').doc(firebaseUser.uid).get();

      final userDto = UserDto.fromFirebaseUser(
        firebaseUser,
        userDoc.exists ? userDoc.data() : null,
      );

      return Right(userDto.toDomain());
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(
        Failure.unknown(message: 'Failed to sign in user', exception: e),
      );
    }
  }

  @override
  Future<Either<Failure, Unit>> signOut() async {
    try {
      await _firebaseAuth.signOut();
      return const Right(unit);
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(
        Failure.unknown(message: 'Failed to sign out user', exception: e),
      );
    }
  }

  @override
  Future<Either<Failure, Unit>> sendEmailVerification() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(
          Failure.auth(message: 'No user is currently signed in'),
        );
      }

      await user.sendEmailVerification();
      return const Right(unit);
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(
        Failure.unknown(
          message: 'Failed to send email verification',
          exception: e,
        ),
      );
    }
  }

  @override
  Future<Either<Failure, bool>> isEmailVerified() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(
          Failure.auth(message: 'No user is currently signed in'),
        );
      }

      await user.reload();
      return Right(user.emailVerified);
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(
        Failure.unknown(
          message: 'Failed to check email verification status',
          exception: e,
        ),
      );
    }
  }

  @override
  Future<Either<Failure, Unit>> sendPasswordResetEmail({
    required EmailAddress email,
  }) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email.value);
      return const Right(unit);
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(
        Failure.unknown(
          message: 'Failed to send password reset email',
          exception: e,
        ),
      );
    }
  }

  @override
  Future<Either<Failure, Unit>> confirmPasswordReset({
    required String code,
    required String newPassword,
  }) async {
    try {
      await _firebaseAuth.confirmPasswordReset(
        code: code,
        newPassword: newPassword,
      );
      return const Right(unit);
    } on firebase_auth.FirebaseAuthException catch (e) {
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      return Left(
        Failure.unknown(message: 'Failed to reset password', exception: e),
      );
    }
  }

  @override
  Future<Either<Failure, bool>> isUsernameAvailable(Username username) async {
    try {
      final query =
          await _firestore
              .collection('users')
              .where('username', isEqualTo: username.value)
              .limit(1)
              .get();

      return Right(query.docs.isEmpty);
    } catch (e) {
      return Left(
        Failure.unknown(
          message: 'Failed to check username availability',
          exception: e,
        ),
      );
    }
  }

  @override
  Future<Either<Failure, Unit>> updateLastLogin(String userId) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'lastLogin': DateTime.now().millisecondsSinceEpoch,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });
      return const Right(unit);
    } catch (e) {
      return Left(
        Failure.unknown(message: 'Failed to update last login', exception: e),
      );
    }
  }

  @override
  Future<Either<Failure, User?>> getUserById(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();

      if (!userDoc.exists) return const Right(null);

      final userDto = UserDto.fromFirestore(userDoc.data()!, userId);
      return Right(userDto.toDomain());
    } catch (e) {
      return Left(
        Failure.unknown(message: 'Failed to get user by ID', exception: e),
      );
    }
  }

  /// Map Firebase Auth exceptions to domain failures
  Failure _mapFirebaseAuthException(firebase_auth.FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return const Failure.auth(
          message: 'No user found with this email address',
          code: 'user-not-found',
        );
      case 'wrong-password':
        return const Failure.auth(
          message: 'Incorrect password',
          code: 'wrong-password',
        );
      case 'email-already-in-use':
        return const Failure.auth(
          message: 'An account already exists with this email address',
          code: 'email-already-in-use',
        );
      case 'weak-password':
        return const Failure.auth(
          message: 'Password is too weak',
          code: 'weak-password',
        );
      case 'invalid-email':
        return const Failure.auth(
          message: 'Invalid email address',
          code: 'invalid-email',
        );
      case 'user-disabled':
        return const Failure.auth(
          message: 'This user account has been disabled',
          code: 'user-disabled',
        );
      case 'too-many-requests':
        return const Failure.auth(
          message: 'Too many requests. Please try again later',
          code: 'too-many-requests',
        );
      case 'network-request-failed':
        return const Failure.network(
          message: 'Network error occurred',
          code: 'network-request-failed',
        );
      default:
        return Failure.auth(
          message: e.message ?? 'Authentication failed',
          code: e.code,
        );
    }
  }

  // Placeholder implementations for remaining methods
  @override
  Future<Either<Failure, User>> updateUserProfile({
    required String userId,
    String? displayName,
    String? bio,
    String? profileImageUrl,
    String? phoneNumber,
    List<String>? categories,
    double? postAmount,
  }) async {
    // TODO: Implement
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, User>> completeProfile({
    required String userId,
    required String displayName,
    String? bio,
    String? profileImageUrl,
    List<String>? categories,
  }) async {
    // TODO: Implement
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, User>> updateUsername({
    required String userId,
    required Username newUsername,
  }) async {
    // TODO: Implement
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, Unit>> deleteAccount() async {
    // TODO: Implement
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, User>> refreshUserData(String userId) async {
    // TODO: Implement
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, List<User>>> getUsersByIds(
    List<String> userIds,
  ) async {
    // TODO: Implement
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, List<User>>> searchUsers({
    required String query,
    int limit = 20,
    String? lastUserId,
  }) async {
    // TODO: Implement
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, Unit>> followUser({
    required String currentUserId,
    required String targetUserId,
  }) async {
    // TODO: Implement
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, Unit>> unfollowUser({
    required String currentUserId,
    required String targetUserId,
  }) async {
    // TODO: Implement
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, List<User>>> getFollowers({
    required String userId,
    int limit = 20,
    String? lastUserId,
  }) async {
    // TODO: Implement
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, List<User>>> getFollowing({
    required String userId,
    int limit = 20,
    String? lastUserId,
  }) async {
    // TODO: Implement
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, Unit>> updateActiveStatus({
    required String userId,
    required bool isActive,
  }) async {
    // TODO: Implement
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, Unit>> verifyPhoneNumber({
    required String phoneNumber,
    required String verificationCode,
  }) async {
    // TODO: Implement
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, Unit>> sendPhoneVerification({
    required String phoneNumber,
  }) async {
    // TODO: Implement
    throw UnimplementedError();
  }
}
