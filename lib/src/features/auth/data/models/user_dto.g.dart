// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserDtoImpl _$$UserDtoImplFromJson(
  Map<String, dynamic> json,
) => _$UserDtoImpl(
  id: json['id'] as String,
  email: json['email'] as String,
  username: json['username'] as String,
  displayName: json['displayName'] as String,
  emailVerified: json['emailVerified'] as bool,
  profileCompleted: json['profileCompleted'] as bool,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt:
      json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
  lastLogin:
      json['lastLogin'] == null
          ? null
          : DateTime.parse(json['lastLogin'] as String),
  profileImageUrl: json['profileImageUrl'] as String?,
  photoUrl: json['photoUrl'] as String?,
  phoneNumber: json['phoneNumber'] as String?,
  bio: json['bio'] as String?,
  postAmount: (json['postAmount'] as num?)?.toDouble() ?? 0.0,
  categories:
      (json['categories'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  followers:
      (json['followers'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  following:
      (json['following'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  followersCount: (json['followersCount'] as num?)?.toInt() ?? 0,
  followingCount: (json['followingCount'] as num?)?.toInt() ?? 0,
  postsCount: (json['postsCount'] as num?)?.toInt() ?? 0,
  isActive: json['isActive'] as bool? ?? true,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$$UserDtoImplToJson(_$UserDtoImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'username': instance.username,
      'displayName': instance.displayName,
      'emailVerified': instance.emailVerified,
      'profileCompleted': instance.profileCompleted,
      'createdAt': instance.createdAt.toIso8601String(),
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
      if (instance.lastLogin?.toIso8601String() case final value?)
        'lastLogin': value,
      if (instance.profileImageUrl case final value?) 'profileImageUrl': value,
      if (instance.photoUrl case final value?) 'photoUrl': value,
      if (instance.phoneNumber case final value?) 'phoneNumber': value,
      if (instance.bio case final value?) 'bio': value,
      'postAmount': instance.postAmount,
      'categories': instance.categories,
      'followers': instance.followers,
      'following': instance.following,
      'followersCount': instance.followersCount,
      'followingCount': instance.followingCount,
      'postsCount': instance.postsCount,
      'isActive': instance.isActive,
      if (instance.metadata case final value?) 'metadata': value,
    };
