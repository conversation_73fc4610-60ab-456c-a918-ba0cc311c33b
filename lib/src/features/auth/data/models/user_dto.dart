import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import '../../../../core/domain/entities.dart';
import '../../domain/entities/user.dart';

part 'user_dto.freezed.dart';
part 'user_dto.g.dart';

/// Data Transfer Object for User entity
@freezed
class UserDto with _$UserDto {
  const factory UserDto({
    required String id,
    required String email,
    required String username,
    required String displayName,
    required bool emailVerified,
    required bool profileCompleted,
    required DateTime createdAt,
    DateTime? updatedAt,
    DateTime? lastLogin,
    String? profileImageUrl,
    String? photoUrl,
    String? phoneNumber,
    String? bio,
    @Default(0.0) double postAmount,
    @Default([]) List<String> categories,
    @Default([]) List<String> followers,
    @Default([]) List<String> following,
    @Default(0) int followersCount,
    @Default(0) int followingCount,
    @Default(0) int postsCount,
    @Default(true) bool isActive,
    Map<String, dynamic>? metadata,
  }) = _UserDto;

  const UserDto._();

  /// Create from JSON
  factory UserDto.fromJson(Map<String, dynamic> json) => _$UserDtoFromJson(json);

  /// Create from Firebase User and Firestore document
  factory UserDto.fromFirebaseUser(
    firebase_auth.User firebaseUser,
    Map<String, dynamic>? firestoreData,
  ) {
    final now = DateTime.now();
    
    return UserDto(
      id: firebaseUser.uid,
      email: firebaseUser.email ?? '',
      username: firestoreData?['username'] ?? '',
      displayName: firestoreData?['displayName'] ?? firebaseUser.displayName ?? '',
      emailVerified: firebaseUser.emailVerified,
      profileCompleted: firestoreData?['profileCompleted'] ?? false,
      createdAt: firestoreData?['createdAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(firestoreData!['createdAt'])
          : firebaseUser.metadata.creationTime ?? now,
      updatedAt: firestoreData?['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(firestoreData!['updatedAt'])
          : null,
      lastLogin: firestoreData?['lastLogin'] != null
          ? DateTime.fromMillisecondsSinceEpoch(firestoreData!['lastLogin'])
          : firebaseUser.metadata.lastSignInTime,
      profileImageUrl: firestoreData?['profileImageUrl'],
      photoUrl: firebaseUser.photoURL,
      phoneNumber: firebaseUser.phoneNumber,
      bio: firestoreData?['bio'],
      postAmount: (firestoreData?['postAmount'] ?? 0.0).toDouble(),
      categories: List<String>.from(firestoreData?['categories'] ?? []),
      followers: List<String>.from(firestoreData?['followers'] ?? []),
      following: List<String>.from(firestoreData?['following'] ?? []),
      followersCount: firestoreData?['followersCount'] ?? 0,
      followingCount: firestoreData?['followingCount'] ?? 0,
      postsCount: firestoreData?['postsCount'] ?? 0,
      isActive: firestoreData?['isActive'] ?? true,
      metadata: firestoreData?['metadata'],
    );
  }

  /// Create from Firestore document only
  factory UserDto.fromFirestore(Map<String, dynamic> data, String id) {
    return UserDto(
      id: id,
      email: data['email'] ?? '',
      username: data['username'] ?? '',
      displayName: data['displayName'] ?? '',
      emailVerified: data['emailVerified'] ?? false,
      profileCompleted: data['profileCompleted'] ?? false,
      createdAt: data['createdAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(data['createdAt'])
          : DateTime.now(),
      updatedAt: data['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(data['updatedAt'])
          : null,
      lastLogin: data['lastLogin'] != null
          ? DateTime.fromMillisecondsSinceEpoch(data['lastLogin'])
          : null,
      profileImageUrl: data['profileImageUrl'],
      photoUrl: data['photoUrl'],
      phoneNumber: data['phoneNumber'],
      bio: data['bio'],
      postAmount: (data['postAmount'] ?? 0.0).toDouble(),
      categories: List<String>.from(data['categories'] ?? []),
      followers: List<String>.from(data['followers'] ?? []),
      following: List<String>.from(data['following'] ?? []),
      followersCount: data['followersCount'] ?? 0,
      followingCount: data['followingCount'] ?? 0,
      postsCount: data['postsCount'] ?? 0,
      isActive: data['isActive'] ?? true,
      metadata: data['metadata'],
    );
  }

  /// Convert to domain entity
  User toDomain() {
    return User(
      id: id,
      email: EmailAddress(email),
      username: Username(username),
      displayName: displayName,
      emailVerified: emailVerified,
      profileCompleted: profileCompleted,
      createdAt: createdAt,
      updatedAt: updatedAt,
      lastLogin: lastLogin,
      profileImageUrl: profileImageUrl,
      photoUrl: photoUrl,
      phoneNumber: phoneNumber,
      bio: bio,
      postAmount: postAmount,
      categories: categories,
      followers: followers,
      following: following,
      followersCount: followersCount,
      followingCount: followingCount,
      postsCount: postsCount,
      isActive: isActive,
      metadata: metadata,
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'email': email,
      'username': username,
      'displayName': displayName,
      'emailVerified': emailVerified,
      'profileCompleted': profileCompleted,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
      'lastLogin': lastLogin?.millisecondsSinceEpoch,
      'profileImageUrl': profileImageUrl,
      'photoUrl': photoUrl,
      'phoneNumber': phoneNumber,
      'bio': bio,
      'postAmount': postAmount,
      'categories': categories,
      'followers': followers,
      'following': following,
      'followersCount': followersCount,
      'followingCount': followingCount,
      'postsCount': postsCount,
      'isActive': isActive,
      'metadata': metadata,
    };
  }
}

/// Extension to convert domain User to DTO
extension UserToDto on User {
  UserDto toDto() {
    return UserDto(
      id: id,
      email: email.value,
      username: username.value,
      displayName: displayName,
      emailVerified: emailVerified,
      profileCompleted: profileCompleted,
      createdAt: createdAt,
      updatedAt: updatedAt,
      lastLogin: lastLogin,
      profileImageUrl: profileImageUrl,
      photoUrl: photoUrl,
      phoneNumber: phoneNumber,
      bio: bio,
      postAmount: postAmount,
      categories: categories,
      followers: followers,
      following: following,
      followersCount: followersCount,
      followingCount: followingCount,
      postsCount: postsCount,
      isActive: isActive,
      metadata: metadata,
    );
  }
}
