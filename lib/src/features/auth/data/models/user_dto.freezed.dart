// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

UserDto _$UserDtoFromJson(Map<String, dynamic> json) {
  return _UserDto.fromJson(json);
}

/// @nodoc
mixin _$UserDto {
  String get id => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String get displayName => throw _privateConstructorUsedError;
  bool get emailVerified => throw _privateConstructorUsedError;
  bool get profileCompleted => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  DateTime? get lastLogin => throw _privateConstructorUsedError;
  String? get profileImageUrl => throw _privateConstructorUsedError;
  String? get photoUrl => throw _privateConstructorUsedError;
  String? get phoneNumber => throw _privateConstructorUsedError;
  String? get bio => throw _privateConstructorUsedError;
  double get postAmount => throw _privateConstructorUsedError;
  List<String> get categories => throw _privateConstructorUsedError;
  List<String> get followers => throw _privateConstructorUsedError;
  List<String> get following => throw _privateConstructorUsedError;
  int get followersCount => throw _privateConstructorUsedError;
  int get followingCount => throw _privateConstructorUsedError;
  int get postsCount => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this UserDto to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserDto
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserDtoCopyWith<UserDto> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserDtoCopyWith<$Res> {
  factory $UserDtoCopyWith(UserDto value, $Res Function(UserDto) then) =
      _$UserDtoCopyWithImpl<$Res, UserDto>;
  @useResult
  $Res call({
    String id,
    String email,
    String username,
    String displayName,
    bool emailVerified,
    bool profileCompleted,
    DateTime createdAt,
    DateTime? updatedAt,
    DateTime? lastLogin,
    String? profileImageUrl,
    String? photoUrl,
    String? phoneNumber,
    String? bio,
    double postAmount,
    List<String> categories,
    List<String> followers,
    List<String> following,
    int followersCount,
    int followingCount,
    int postsCount,
    bool isActive,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class _$UserDtoCopyWithImpl<$Res, $Val extends UserDto>
    implements $UserDtoCopyWith<$Res> {
  _$UserDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserDto
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? username = null,
    Object? displayName = null,
    Object? emailVerified = null,
    Object? profileCompleted = null,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? lastLogin = freezed,
    Object? profileImageUrl = freezed,
    Object? photoUrl = freezed,
    Object? phoneNumber = freezed,
    Object? bio = freezed,
    Object? postAmount = null,
    Object? categories = null,
    Object? followers = null,
    Object? following = null,
    Object? followersCount = null,
    Object? followingCount = null,
    Object? postsCount = null,
    Object? isActive = null,
    Object? metadata = freezed,
  }) {
    return _then(
      _value.copyWith(
            id:
                null == id
                    ? _value.id
                    : id // ignore: cast_nullable_to_non_nullable
                        as String,
            email:
                null == email
                    ? _value.email
                    : email // ignore: cast_nullable_to_non_nullable
                        as String,
            username:
                null == username
                    ? _value.username
                    : username // ignore: cast_nullable_to_non_nullable
                        as String,
            displayName:
                null == displayName
                    ? _value.displayName
                    : displayName // ignore: cast_nullable_to_non_nullable
                        as String,
            emailVerified:
                null == emailVerified
                    ? _value.emailVerified
                    : emailVerified // ignore: cast_nullable_to_non_nullable
                        as bool,
            profileCompleted:
                null == profileCompleted
                    ? _value.profileCompleted
                    : profileCompleted // ignore: cast_nullable_to_non_nullable
                        as bool,
            createdAt:
                null == createdAt
                    ? _value.createdAt
                    : createdAt // ignore: cast_nullable_to_non_nullable
                        as DateTime,
            updatedAt:
                freezed == updatedAt
                    ? _value.updatedAt
                    : updatedAt // ignore: cast_nullable_to_non_nullable
                        as DateTime?,
            lastLogin:
                freezed == lastLogin
                    ? _value.lastLogin
                    : lastLogin // ignore: cast_nullable_to_non_nullable
                        as DateTime?,
            profileImageUrl:
                freezed == profileImageUrl
                    ? _value.profileImageUrl
                    : profileImageUrl // ignore: cast_nullable_to_non_nullable
                        as String?,
            photoUrl:
                freezed == photoUrl
                    ? _value.photoUrl
                    : photoUrl // ignore: cast_nullable_to_non_nullable
                        as String?,
            phoneNumber:
                freezed == phoneNumber
                    ? _value.phoneNumber
                    : phoneNumber // ignore: cast_nullable_to_non_nullable
                        as String?,
            bio:
                freezed == bio
                    ? _value.bio
                    : bio // ignore: cast_nullable_to_non_nullable
                        as String?,
            postAmount:
                null == postAmount
                    ? _value.postAmount
                    : postAmount // ignore: cast_nullable_to_non_nullable
                        as double,
            categories:
                null == categories
                    ? _value.categories
                    : categories // ignore: cast_nullable_to_non_nullable
                        as List<String>,
            followers:
                null == followers
                    ? _value.followers
                    : followers // ignore: cast_nullable_to_non_nullable
                        as List<String>,
            following:
                null == following
                    ? _value.following
                    : following // ignore: cast_nullable_to_non_nullable
                        as List<String>,
            followersCount:
                null == followersCount
                    ? _value.followersCount
                    : followersCount // ignore: cast_nullable_to_non_nullable
                        as int,
            followingCount:
                null == followingCount
                    ? _value.followingCount
                    : followingCount // ignore: cast_nullable_to_non_nullable
                        as int,
            postsCount:
                null == postsCount
                    ? _value.postsCount
                    : postsCount // ignore: cast_nullable_to_non_nullable
                        as int,
            isActive:
                null == isActive
                    ? _value.isActive
                    : isActive // ignore: cast_nullable_to_non_nullable
                        as bool,
            metadata:
                freezed == metadata
                    ? _value.metadata
                    : metadata // ignore: cast_nullable_to_non_nullable
                        as Map<String, dynamic>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$UserDtoImplCopyWith<$Res> implements $UserDtoCopyWith<$Res> {
  factory _$$UserDtoImplCopyWith(
    _$UserDtoImpl value,
    $Res Function(_$UserDtoImpl) then,
  ) = __$$UserDtoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String email,
    String username,
    String displayName,
    bool emailVerified,
    bool profileCompleted,
    DateTime createdAt,
    DateTime? updatedAt,
    DateTime? lastLogin,
    String? profileImageUrl,
    String? photoUrl,
    String? phoneNumber,
    String? bio,
    double postAmount,
    List<String> categories,
    List<String> followers,
    List<String> following,
    int followersCount,
    int followingCount,
    int postsCount,
    bool isActive,
    Map<String, dynamic>? metadata,
  });
}

/// @nodoc
class __$$UserDtoImplCopyWithImpl<$Res>
    extends _$UserDtoCopyWithImpl<$Res, _$UserDtoImpl>
    implements _$$UserDtoImplCopyWith<$Res> {
  __$$UserDtoImplCopyWithImpl(
    _$UserDtoImpl _value,
    $Res Function(_$UserDtoImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of UserDto
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? username = null,
    Object? displayName = null,
    Object? emailVerified = null,
    Object? profileCompleted = null,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? lastLogin = freezed,
    Object? profileImageUrl = freezed,
    Object? photoUrl = freezed,
    Object? phoneNumber = freezed,
    Object? bio = freezed,
    Object? postAmount = null,
    Object? categories = null,
    Object? followers = null,
    Object? following = null,
    Object? followersCount = null,
    Object? followingCount = null,
    Object? postsCount = null,
    Object? isActive = null,
    Object? metadata = freezed,
  }) {
    return _then(
      _$UserDtoImpl(
        id:
            null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                    as String,
        email:
            null == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                    as String,
        username:
            null == username
                ? _value.username
                : username // ignore: cast_nullable_to_non_nullable
                    as String,
        displayName:
            null == displayName
                ? _value.displayName
                : displayName // ignore: cast_nullable_to_non_nullable
                    as String,
        emailVerified:
            null == emailVerified
                ? _value.emailVerified
                : emailVerified // ignore: cast_nullable_to_non_nullable
                    as bool,
        profileCompleted:
            null == profileCompleted
                ? _value.profileCompleted
                : profileCompleted // ignore: cast_nullable_to_non_nullable
                    as bool,
        createdAt:
            null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                    as DateTime,
        updatedAt:
            freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                    as DateTime?,
        lastLogin:
            freezed == lastLogin
                ? _value.lastLogin
                : lastLogin // ignore: cast_nullable_to_non_nullable
                    as DateTime?,
        profileImageUrl:
            freezed == profileImageUrl
                ? _value.profileImageUrl
                : profileImageUrl // ignore: cast_nullable_to_non_nullable
                    as String?,
        photoUrl:
            freezed == photoUrl
                ? _value.photoUrl
                : photoUrl // ignore: cast_nullable_to_non_nullable
                    as String?,
        phoneNumber:
            freezed == phoneNumber
                ? _value.phoneNumber
                : phoneNumber // ignore: cast_nullable_to_non_nullable
                    as String?,
        bio:
            freezed == bio
                ? _value.bio
                : bio // ignore: cast_nullable_to_non_nullable
                    as String?,
        postAmount:
            null == postAmount
                ? _value.postAmount
                : postAmount // ignore: cast_nullable_to_non_nullable
                    as double,
        categories:
            null == categories
                ? _value._categories
                : categories // ignore: cast_nullable_to_non_nullable
                    as List<String>,
        followers:
            null == followers
                ? _value._followers
                : followers // ignore: cast_nullable_to_non_nullable
                    as List<String>,
        following:
            null == following
                ? _value._following
                : following // ignore: cast_nullable_to_non_nullable
                    as List<String>,
        followersCount:
            null == followersCount
                ? _value.followersCount
                : followersCount // ignore: cast_nullable_to_non_nullable
                    as int,
        followingCount:
            null == followingCount
                ? _value.followingCount
                : followingCount // ignore: cast_nullable_to_non_nullable
                    as int,
        postsCount:
            null == postsCount
                ? _value.postsCount
                : postsCount // ignore: cast_nullable_to_non_nullable
                    as int,
        isActive:
            null == isActive
                ? _value.isActive
                : isActive // ignore: cast_nullable_to_non_nullable
                    as bool,
        metadata:
            freezed == metadata
                ? _value._metadata
                : metadata // ignore: cast_nullable_to_non_nullable
                    as Map<String, dynamic>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$UserDtoImpl extends _UserDto {
  const _$UserDtoImpl({
    required this.id,
    required this.email,
    required this.username,
    required this.displayName,
    required this.emailVerified,
    required this.profileCompleted,
    required this.createdAt,
    this.updatedAt,
    this.lastLogin,
    this.profileImageUrl,
    this.photoUrl,
    this.phoneNumber,
    this.bio,
    this.postAmount = 0.0,
    final List<String> categories = const [],
    final List<String> followers = const [],
    final List<String> following = const [],
    this.followersCount = 0,
    this.followingCount = 0,
    this.postsCount = 0,
    this.isActive = true,
    final Map<String, dynamic>? metadata,
  }) : _categories = categories,
       _followers = followers,
       _following = following,
       _metadata = metadata,
       super._();

  factory _$UserDtoImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserDtoImplFromJson(json);

  @override
  final String id;
  @override
  final String email;
  @override
  final String username;
  @override
  final String displayName;
  @override
  final bool emailVerified;
  @override
  final bool profileCompleted;
  @override
  final DateTime createdAt;
  @override
  final DateTime? updatedAt;
  @override
  final DateTime? lastLogin;
  @override
  final String? profileImageUrl;
  @override
  final String? photoUrl;
  @override
  final String? phoneNumber;
  @override
  final String? bio;
  @override
  @JsonKey()
  final double postAmount;
  final List<String> _categories;
  @override
  @JsonKey()
  List<String> get categories {
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_categories);
  }

  final List<String> _followers;
  @override
  @JsonKey()
  List<String> get followers {
    if (_followers is EqualUnmodifiableListView) return _followers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_followers);
  }

  final List<String> _following;
  @override
  @JsonKey()
  List<String> get following {
    if (_following is EqualUnmodifiableListView) return _following;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_following);
  }

  @override
  @JsonKey()
  final int followersCount;
  @override
  @JsonKey()
  final int followingCount;
  @override
  @JsonKey()
  final int postsCount;
  @override
  @JsonKey()
  final bool isActive;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'UserDto(id: $id, email: $email, username: $username, displayName: $displayName, emailVerified: $emailVerified, profileCompleted: $profileCompleted, createdAt: $createdAt, updatedAt: $updatedAt, lastLogin: $lastLogin, profileImageUrl: $profileImageUrl, photoUrl: $photoUrl, phoneNumber: $phoneNumber, bio: $bio, postAmount: $postAmount, categories: $categories, followers: $followers, following: $following, followersCount: $followersCount, followingCount: $followingCount, postsCount: $postsCount, isActive: $isActive, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserDtoImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.emailVerified, emailVerified) ||
                other.emailVerified == emailVerified) &&
            (identical(other.profileCompleted, profileCompleted) ||
                other.profileCompleted == profileCompleted) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.lastLogin, lastLogin) ||
                other.lastLogin == lastLogin) &&
            (identical(other.profileImageUrl, profileImageUrl) ||
                other.profileImageUrl == profileImageUrl) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.bio, bio) || other.bio == bio) &&
            (identical(other.postAmount, postAmount) ||
                other.postAmount == postAmount) &&
            const DeepCollectionEquality().equals(
              other._categories,
              _categories,
            ) &&
            const DeepCollectionEquality().equals(
              other._followers,
              _followers,
            ) &&
            const DeepCollectionEquality().equals(
              other._following,
              _following,
            ) &&
            (identical(other.followersCount, followersCount) ||
                other.followersCount == followersCount) &&
            (identical(other.followingCount, followingCount) ||
                other.followingCount == followingCount) &&
            (identical(other.postsCount, postsCount) ||
                other.postsCount == postsCount) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    email,
    username,
    displayName,
    emailVerified,
    profileCompleted,
    createdAt,
    updatedAt,
    lastLogin,
    profileImageUrl,
    photoUrl,
    phoneNumber,
    bio,
    postAmount,
    const DeepCollectionEquality().hash(_categories),
    const DeepCollectionEquality().hash(_followers),
    const DeepCollectionEquality().hash(_following),
    followersCount,
    followingCount,
    postsCount,
    isActive,
    const DeepCollectionEquality().hash(_metadata),
  ]);

  /// Create a copy of UserDto
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserDtoImplCopyWith<_$UserDtoImpl> get copyWith =>
      __$$UserDtoImplCopyWithImpl<_$UserDtoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserDtoImplToJson(this);
  }
}

abstract class _UserDto extends UserDto {
  const factory _UserDto({
    required final String id,
    required final String email,
    required final String username,
    required final String displayName,
    required final bool emailVerified,
    required final bool profileCompleted,
    required final DateTime createdAt,
    final DateTime? updatedAt,
    final DateTime? lastLogin,
    final String? profileImageUrl,
    final String? photoUrl,
    final String? phoneNumber,
    final String? bio,
    final double postAmount,
    final List<String> categories,
    final List<String> followers,
    final List<String> following,
    final int followersCount,
    final int followingCount,
    final int postsCount,
    final bool isActive,
    final Map<String, dynamic>? metadata,
  }) = _$UserDtoImpl;
  const _UserDto._() : super._();

  factory _UserDto.fromJson(Map<String, dynamic> json) = _$UserDtoImpl.fromJson;

  @override
  String get id;
  @override
  String get email;
  @override
  String get username;
  @override
  String get displayName;
  @override
  bool get emailVerified;
  @override
  bool get profileCompleted;
  @override
  DateTime get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  DateTime? get lastLogin;
  @override
  String? get profileImageUrl;
  @override
  String? get photoUrl;
  @override
  String? get phoneNumber;
  @override
  String? get bio;
  @override
  double get postAmount;
  @override
  List<String> get categories;
  @override
  List<String> get followers;
  @override
  List<String> get following;
  @override
  int get followersCount;
  @override
  int get followingCount;
  @override
  int get postsCount;
  @override
  bool get isActive;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of UserDto
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserDtoImplCopyWith<_$UserDtoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
