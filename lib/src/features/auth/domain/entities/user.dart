import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../core/domain/entities.dart';

part 'user.freezed.dart';

@freezed
class User with _$User implements Entity {
  const factory User({
    required String id,
    required EmailAddress email,
    required Username username,
    required String displayName,
    required bool emailVerified,
    required bool profileCompleted,
    required DateTime createdAt,
    DateTime? updatedAt,
    DateTime? lastLogin,
    String? profileImageUrl,
    String? photoUrl,
    String? phoneNumber,
    String? bio,
    @Default(0.0) double postAmount,
    @Default([]) List<String> categories,
    @Default([]) List<String> followers,
    @Default([]) List<String> following,
    @Default(0) int followersCount,
    @Default(0) int followingCount,
    @Default(0) int postsCount,
    @Default(true) bool isActive,
    Map<String, dynamic>? metadata,
  }) = _User;

  const User._();

  /// Check if user has completed onboarding
  bool get hasCompletedOnboarding => 
      profileCompleted && username.value.isNotEmpty;

  /// Check if user can create posts
  bool get canCreatePosts => 
      emailVerified && profileCompleted && isActive;

  /// Get display name or fallback to username
  String get effectiveDisplayName => 
      displayName.isNotEmpty ? displayName : username.value;

  /// Get profile image URL with fallback
  String? get effectiveProfileImageUrl => 
      profileImageUrl ?? photoUrl;

  /// Check if user is following another user
  bool isFollowing(String userId) => following.contains(userId);

  /// Check if user is followed by another user
  bool isFollowedBy(String userId) => followers.contains(userId);

  /// Create a copy with updated last login
  User withUpdatedLastLogin() => copyWith(
    lastLogin: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  /// Create a copy with completed profile
  User withCompletedProfile({
    required String displayName,
    String? bio,
    String? profileImageUrl,
  }) => copyWith(
    displayName: displayName,
    bio: bio,
    profileImageUrl: profileImageUrl,
    profileCompleted: true,
    updatedAt: DateTime.now(),
  );

  /// Create a copy with verified email
  User withVerifiedEmail() => copyWith(
    emailVerified: true,
    updatedAt: DateTime.now(),
  );

  /// Create a copy with updated categories
  User withUpdatedCategories(List<String> newCategories) => copyWith(
    categories: newCategories,
    updatedAt: DateTime.now(),
  );

  /// Create a copy with updated post amount
  User withUpdatedPostAmount(double amount) => copyWith(
    postAmount: amount,
    updatedAt: DateTime.now(),
  );

  /// Create a copy with new follower
  User withNewFollower(String userId) {
    if (followers.contains(userId)) return this;
    return copyWith(
      followers: [...followers, userId],
      followersCount: followersCount + 1,
      updatedAt: DateTime.now(),
    );
  }

  /// Create a copy with removed follower
  User withRemovedFollower(String userId) {
    if (!followers.contains(userId)) return this;
    return copyWith(
      followers: followers.where((id) => id != userId).toList(),
      followersCount: followersCount - 1,
      updatedAt: DateTime.now(),
    );
  }

  /// Create a copy with new following
  User withNewFollowing(String userId) {
    if (following.contains(userId)) return this;
    return copyWith(
      following: [...following, userId],
      followingCount: followingCount + 1,
      updatedAt: DateTime.now(),
    );
  }

  /// Create a copy with removed following
  User withRemovedFollowing(String userId) {
    if (!following.contains(userId)) return this;
    return copyWith(
      following: following.where((id) => id != userId).toList(),
      followingCount: followingCount - 1,
      updatedAt: DateTime.now(),
    );
  }

  /// Create a copy with incremented posts count
  User withIncrementedPostsCount() => copyWith(
    postsCount: postsCount + 1,
    updatedAt: DateTime.now(),
  );

  /// Create a copy with decremented posts count
  User withDecrementedPostsCount() => copyWith(
    postsCount: postsCount > 0 ? postsCount - 1 : 0,
    updatedAt: DateTime.now(),
  );
}
