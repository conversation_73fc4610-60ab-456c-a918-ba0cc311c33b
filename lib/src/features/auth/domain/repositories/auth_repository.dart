import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/domain/entities.dart';
import '../entities/user.dart';

/// Abstract repository interface for authentication operations
abstract class AuthRepository {
  /// Stream of authentication state changes
  Stream<User?> get authStateChanges;

  /// Get current authenticated user
  Future<Either<Failure, User?>> getCurrentUser();

  /// Sign up with email and password
  Future<Either<Failure, User>> signUpWithEmailAndPassword({
    required EmailAddress email,
    required String password,
    required Username username,
    required String displayName,
  });

  /// Sign in with email and password
  Future<Either<Failure, User>> signInWithEmailAndPassword({
    required EmailAddress email,
    required String password,
  });

  /// Sign out current user
  Future<Either<Failure, Unit>> signOut();

  /// Send email verification
  Future<Either<Failure, Unit>> sendEmailVerification();

  /// Check if email is verified
  Future<Either<Failure, bool>> isEmailVerified();

  /// Send password reset email
  Future<Either<Failure, Unit>> sendPasswordResetEmail({
    required EmailAddress email,
  });

  /// Confirm password reset with code
  Future<Either<Failure, Unit>> confirmPasswordReset({
    required String code,
    required String newPassword,
  });

  /// Update user profile
  Future<Either<Failure, User>> updateUserProfile({
    required String userId,
    String? displayName,
    String? bio,
    String? profileImageUrl,
    String? phoneNumber,
    List<String>? categories,
    double? postAmount,
  });

  /// Complete user profile setup
  Future<Either<Failure, User>> completeProfile({
    required String userId,
    required String displayName,
    String? bio,
    String? profileImageUrl,
    List<String>? categories,
  });

  /// Check if username is available
  Future<Either<Failure, bool>> isUsernameAvailable(Username username);

  /// Update username
  Future<Either<Failure, User>> updateUsername({
    required String userId,
    required Username newUsername,
  });

  /// Delete user account
  Future<Either<Failure, Unit>> deleteAccount();

  /// Refresh user data from server
  Future<Either<Failure, User>> refreshUserData(String userId);

  /// Update user's last login timestamp
  Future<Either<Failure, Unit>> updateLastLogin(String userId);

  /// Get user by ID
  Future<Either<Failure, User?>> getUserById(String userId);

  /// Get multiple users by IDs
  Future<Either<Failure, List<User>>> getUsersByIds(List<String> userIds);

  /// Search users by username or display name
  Future<Either<Failure, List<User>>> searchUsers({
    required String query,
    int limit = 20,
    String? lastUserId,
  });

  /// Follow a user
  Future<Either<Failure, Unit>> followUser({
    required String currentUserId,
    required String targetUserId,
  });

  /// Unfollow a user
  Future<Either<Failure, Unit>> unfollowUser({
    required String currentUserId,
    required String targetUserId,
  });

  /// Get user's followers
  Future<Either<Failure, List<User>>> getFollowers({
    required String userId,
    int limit = 20,
    String? lastUserId,
  });

  /// Get user's following
  Future<Either<Failure, List<User>>> getFollowing({
    required String userId,
    int limit = 20,
    String? lastUserId,
  });

  /// Update user's active status
  Future<Either<Failure, Unit>> updateActiveStatus({
    required String userId,
    required bool isActive,
  });

  /// Verify OTP for phone number
  Future<Either<Failure, Unit>> verifyPhoneNumber({
    required String phoneNumber,
    required String verificationCode,
  });

  /// Send OTP to phone number
  Future<Either<Failure, Unit>> sendPhoneVerification({
    required String phoneNumber,
  });
}
