import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../repositories/auth_repository.dart';

/// Use case for signing out the current user
class SignOutUseCase {
  final AuthRepository _repository;

  const SignOutUseCase(this._repository);

  /// Execute sign out
  Future<Either<Failure, Unit>> call() async {
    try {
      return await _repository.signOut();
    } catch (e) {
      return Left(Failure.unknown(
        message: 'Unexpected error during sign out',
        exception: e,
      ));
    }
  }
}
