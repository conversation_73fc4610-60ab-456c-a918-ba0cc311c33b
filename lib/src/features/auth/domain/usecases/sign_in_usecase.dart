import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/domain/entities.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

/// Use case for signing in a user
class SignInUseCase {
  final AuthRepository _repository;

  const SignInUseCase(this._repository);

  /// Execute sign in with email and password
  Future<Either<Failure, User>> call(SignInParams params) async {
    // Validate input parameters
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      return Left(validationResult);
    }

    try {
      // Attempt to sign in
      final result = await _repository.signInWithEmailAndPassword(
        email: params.email,
        password: params.password,
      );

      return result.fold(
        (failure) => Left(failure),
        (user) async {
          // Update last login timestamp
          await _repository.updateLastLogin(user.id);
          
          // Return user with updated last login
          return Right(user.withUpdatedLastLogin());
        },
      );
    } catch (e) {
      return Left(Failure.unknown(
        message: 'Unexpected error during sign in',
        exception: e,
      ));
    }
  }

  /// Validate sign in parameters
  Failure? _validateParams(SignInParams params) {
    if (params.password.isEmpty) {
      return const Failure.validation(
        message: 'Password cannot be empty',
        field: 'password',
      );
    }

    if (params.password.length < 6) {
      return const Failure.validation(
        message: 'Password must be at least 6 characters long',
        field: 'password',
      );
    }

    return null;
  }
}

/// Parameters for sign in use case
class SignInParams {
  final EmailAddress email;
  final String password;

  const SignInParams({
    required this.email,
    required this.password,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SignInParams &&
          runtimeType == other.runtimeType &&
          email == other.email &&
          password == other.password;

  @override
  int get hashCode => email.hashCode ^ password.hashCode;

  @override
  String toString() => 'SignInParams(email: $email, password: [HIDDEN])';
}
