import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/domain/entities.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

/// Use case for signing up a new user
class SignUpUseCase {
  final AuthRepository _repository;

  const SignUpUseCase(this._repository);

  /// Execute sign up with email and password
  Future<Either<Failure, User>> call(SignUpParams params) async {
    // Validate input parameters
    final validationResult = _validateParams(params);
    if (validationResult != null) {
      return Left(validationResult);
    }

    try {
      // Check if username is available
      final usernameCheck = await _repository.isUsernameAvailable(params.username);
      final isUsernameAvailable = usernameCheck.fold(
        (failure) => false,
        (available) => available,
      );

      if (!isUsernameAvailable) {
        return const Left(Failure.validation(
          message: 'Username is already taken',
          field: 'username',
        ));
      }

      // Attempt to sign up
      final result = await _repository.signUpWithEmailAndPassword(
        email: params.email,
        password: params.password,
        username: params.username,
        displayName: params.displayName,
      );

      return result.fold(
        (failure) => Left(failure),
        (user) async {
          // Send email verification
          await _repository.sendEmailVerification();
          
          return Right(user);
        },
      );
    } catch (e) {
      return Left(Failure.unknown(
        message: 'Unexpected error during sign up',
        exception: e,
      ));
    }
  }

  /// Validate sign up parameters
  Failure? _validateParams(SignUpParams params) {
    if (params.password.isEmpty) {
      return const Failure.validation(
        message: 'Password cannot be empty',
        field: 'password',
      );
    }

    if (params.password.length < 8) {
      return const Failure.validation(
        message: 'Password must be at least 8 characters long',
        field: 'password',
      );
    }

    if (params.displayName.trim().isEmpty) {
      return const Failure.validation(
        message: 'Display name cannot be empty',
        field: 'displayName',
      );
    }

    if (params.displayName.trim().length < 2) {
      return const Failure.validation(
        message: 'Display name must be at least 2 characters long',
        field: 'displayName',
      );
    }

    // Check password strength
    if (!_isPasswordStrong(params.password)) {
      return const Failure.validation(
        message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
        field: 'password',
      );
    }

    return null;
  }

  /// Check if password meets strength requirements
  bool _isPasswordStrong(String password) {
    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    final hasDigits = password.contains(RegExp(r'[0-9]'));
    
    return hasUppercase && hasLowercase && hasDigits;
  }
}

/// Parameters for sign up use case
class SignUpParams {
  final EmailAddress email;
  final String password;
  final Username username;
  final String displayName;

  const SignUpParams({
    required this.email,
    required this.password,
    required this.username,
    required this.displayName,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SignUpParams &&
          runtimeType == other.runtimeType &&
          email == other.email &&
          password == other.password &&
          username == other.username &&
          displayName == other.displayName;

  @override
  int get hashCode =>
      email.hashCode ^
      password.hashCode ^
      username.hashCode ^
      displayName.hashCode;

  @override
  String toString() => 'SignUpParams(email: $email, username: $username, displayName: $displayName, password: [HIDDEN])';
}
