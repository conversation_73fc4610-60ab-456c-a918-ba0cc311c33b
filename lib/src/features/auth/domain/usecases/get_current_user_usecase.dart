import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

/// Use case for getting the current authenticated user
class GetCurrentUserUseCase {
  final AuthRepository _repository;

  const GetCurrentUserUseCase(this._repository);

  /// Execute get current user
  Future<Either<Failure, User?>> call() async {
    try {
      return await _repository.getCurrentUser();
    } catch (e) {
      return Left(Failure.unknown(
        message: 'Unexpected error while getting current user',
        exception: e,
      ));
    }
  }

  /// Get auth state changes stream
  Stream<User?> get authStateChanges => _repository.authStateChanges;
}
