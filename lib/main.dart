import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'src/core/providers/core_providers.dart';
import 'src/features/auth/application/auth_wrapper.dart';
import 'screens/sign_up.dart';
import 'screens/login.dart';
import 'screens/create_account.dart';
import 'screens/create_profile.dart';
import 'screens/category_selection.dart';
import 'screens/choose_username.dart';
import 'screens/otp_verification.dart';
import 'screens/main_navigation_screen.dart';
import 'screens/create_post.dart';
import 'screens/post_feed.dart';
import 'screens/categories_ranking.dart';
import 'screens/wallet_screen.dart';

import 'screens/landing_page.dart';
import 'screens/support_page.dart';
import 'screens/privacy_policy_page.dart';
import 'screens/terms_of_service_page.dart';
import 'screens/contact_page.dart';
import 'screens/about_screen.dart';

import 'screens/search_screen.dart';
import 'screens/chat_list_screen.dart';
import 'src/features/wallet/presentation/screens/wallet_demo_screen.dart';
import 'screens/edit_profile_screen.dart';
import 'screens/connect_screen.dart';

void main() async {
  //hide status bar for the screen
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.white,
      statusBarIconBrightness: Brightness.dark, //for dark icons
    ),
  );

  //initialize firebase
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Initialize SharedPreferences
  final sharedPreferences = await SharedPreferences.getInstance();

  runApp(
    ProviderScope(
      overrides: [
        sharedPreferencesProvider.overrideWithValue(sharedPreferences),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Money Mouthy',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const AuthWrapper(),
      routes: {
        '/landing': (context) => const LandingPage(),
        '/signup': (context) => const SignUpScreen(),
        '/login': (context) => const LoginScreen(),
        '/create-account': (context) => const CreateAccountScreen(),
        '/home': (context) => const MainNavigationScreen(),
        '/create_profile': (context) => const CreateProfileScreen(),
        '/category_selection': (context) => const CategorySelectionScreen(),
        '/choose_username': (context) => const ChooseUsernameScreen(),
        '/otp_verification':
            (context) => const OtpVerificationScreen(email: ''),
        '/create_post': (context) => const CreatePostScreen(),
        '/post_feed': (context) => const PostFeedScreen(),
        '/categories_ranking': (context) => const CategoriesRankingScreen(),
        '/wallet': (context) => const WalletScreen(),
        '/wallet_demo': (context) => const WalletDemoScreen(),
        '/search': (context) => const SearchScreen(),
        '/chats': (context) => const ChatListScreen(),
        '/edit_profile': (context) => const EditProfileScreen(),
        '/connect': (context) => ConnectScreen(),
        '/support': (context) => const SupportPage(),
        '/privacy': (context) => const PrivacyPolicyPage(),
        '/terms': (context) => const TermsOfServicePage(),
        '/contact': (context) => const ContactPage(),
        '/about': (context) => const AboutScreen(),
      },
    );
  }
}

// AuthWrapper is now imported from src/features/auth/application/auth_wrapper.dart

// SplashScreen is now handled by the new AuthWrapper
