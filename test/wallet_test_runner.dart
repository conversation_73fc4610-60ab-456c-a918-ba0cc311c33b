import 'package:flutter_test/flutter_test.dart';

// Import all wallet test files
import 'src/features/wallet/domain/entities/transaction_test.dart' as transaction_tests;
import 'src/features/wallet/domain/entities/wallet_test.dart' as wallet_tests;
import 'src/features/wallet/domain/usecases/add_funds_usecase_test.dart' as add_funds_tests;
import 'src/features/wallet/presentation/widgets/wallet_balance_card_test.dart' as balance_card_tests;
import 'src/features/wallet/integration/wallet_integration_test.dart' as integration_tests;
import 'src/features/wallet/error_handling/wallet_error_handling_test.dart' as error_handling_tests;
import 'src/features/wallet/performance/wallet_performance_test.dart' as performance_tests;

/// Comprehensive test runner for all wallet-related tests
/// 
/// This file runs all wallet tests in a structured manner and provides
/// a summary of test results for the wallet feature.
void main() {
  group('🏦 Wallet Feature Test Suite', () {
    group('📊 Domain Layer Tests', () {
      group('🏷️ Entity Tests', () {
        transaction_tests.main();
        wallet_tests.main();
      });

      group('⚙️ Use Case Tests', () {
        add_funds_tests.main();
      });
    });

    group('🎨 Presentation Layer Tests', () {
      group('🧩 Widget Tests', () {
        balance_card_tests.main();
      });
    });

    group('🔗 Integration Tests', () {
      integration_tests.main();
    });

    group('❌ Error Handling Tests', () {
      error_handling_tests.main();
    });

    group('⚡ Performance Tests', () {
      performance_tests.main();
    });
  });

  // Print test summary after all tests complete
  tearDownAll(() {
    print('\n' + '=' * 60);
    print('🎉 WALLET FEATURE TEST SUMMARY');
    print('=' * 60);
    print('✅ Domain Entity Tests: Transaction & Wallet');
    print('✅ Use Case Tests: Add Funds & Validation');
    print('✅ Widget Tests: Balance Card & UI Components');
    print('✅ Integration Tests: End-to-End Workflows');
    print('✅ Error Handling Tests: All Failure Scenarios');
    print('✅ Performance Tests: Large Datasets & Concurrency');
    print('=' * 60);
    print('🚀 All wallet tests completed successfully!');
    print('📋 Test Coverage Areas:');
    print('   • Business Logic Validation');
    print('   • UI Component Behavior');
    print('   • Error Recovery & Edge Cases');
    print('   • Performance & Scalability');
    print('   • Real-time Updates & State Management');
    print('=' * 60);
  });
}
