name: money_mouthy_two
description: "A new Flutter project."
publish_to: "none"

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8

  # State Management & Architecture
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  freezed: ^2.4.6
  freezed_annotation: ^2.4.1
  dartz: ^0.10.1
  json_annotation: ^4.8.1

  # Firebase
  firebase_core: ^2.27.0
  firebase_auth: ^4.18.0
  cloud_firestore: ^4.15.0
  firebase_storage: ^11.6.0

  # Storage
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0
  sqflite: ^2.3.0

  # Payment Gateway
  flutter_stripe: ^10.1.1
  http: ^1.1.0

  # Utilities
  path: ^1.8.3
  intl: ^0.19.0
  image_picker: ^1.1.1
  font_awesome_flutter: ^10.8.0
  share_plus: ^10.0.0
  url_launcher: ^6.1.11
  video_player: ^2.10.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.13.1

  # Code Generation
  build_runner: ^2.4.7
  riverpod_generator: ^2.3.9
  json_serializable: ^6.7.1
  mocktail: ^1.0.2

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/money_mouth.png"
  min_sdk_android: 21 # android min sdk min: 21

flutter:
  uses-material-design: true
  assets:
    - assets/images/
