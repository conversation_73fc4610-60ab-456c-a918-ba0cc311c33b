targets:
  $default:
    builders:
      riverpod_generator:
        options:
          # The name of the function to use to read providers
          # (defaults to 'ref')
          provider_name: ref
          # The name of the function to use to read providers
          # (defaults to 'watch')
          provider_watch_name: watch
      freezed:
        options:
          # Generates a copyWith method
          copy_with: true
          # Generates an == operator
          equal: true
          # Generates a toString method
          to_string: true
          # Generates a hashCode getter
          hash_code: true
      json_serializable:
        options:
          # Generates explicit toJson methods
          explicit_to_json: true
          # Includes fields with null values in JSON
          include_if_null: false
          # Creates a factory constructor for fromJson
          create_factory: true
